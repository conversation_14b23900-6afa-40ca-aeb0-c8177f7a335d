########################
# .NET Core / Blazor #
########################
**/bin/
**/obj/
**/GeneratedFiles/
**/.vs/
**/.vscode/
**/.idea/
**/.user/
*.user
*.suo
*.userosscache
*.sln.docstates
*.dbmdl

########################
# Rider / JetBrains #
########################
.idea/
*.sln.iml

########################
# Build Artifacts #
########################
*.dll
*.exe
*.pdb
*.cache
*.log
*.tmp
*.bak
*.swp
*.scc

########################
# ASP.NET & Blazor #
########################
wwwroot/lib/
node_modules/
bundleconfig.json
project.lock.json
project.fragment.lock.json
artifacts/

########################
# Testing /
########################
TestResults/
coverage/
*.coverage
*.coveragexml
*.testsettings
*.vsmdi
*.psess

########################
# Docker / Container #
########################
**/.docker/
docker-compose.override.yml
docker-compose.*.yml
.env
.env.*

########################
# Secret Management #
########################
secrets.json
appsettings.Local.json
appsettings.Development.json

########################
# Local Tools / Misc #
########################
*.ncrunchproject
_ReSharper.*
*.resharper
.vs/
.vscode/
*.code-workspace
*.DS_Store
*.Thumbs.db
*.cursor-free-vip/

########################
# Logs and Debugging #
########################
*.log
logs/
*.stackdumpDaVinci/
DaVinci/
*.db
netbox_devices (8).csv
.cursor/
IMPLEMENTATION.md
PROGRESS.md
tailwindcss