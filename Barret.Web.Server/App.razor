﻿@using Barret.Web.Server.Services
@using Barret.Web.Server.Features.Shared.Components.Layout

<CascadingAuthenticationState>
    <Router AppAssembly="@typeof(App).Assembly">
        <Found Context="routeData">
            <AuthorizeRouteView RouteData="@routeData" DefaultLayout="@typeof(Barret.Web.Server.Features.Shared.Components.Layout.MainLayout)">
                <NotAuthorized>
                    @if (context.User.Identity?.IsAuthenticated != true)
                    {
                        <RedirectToLogin />
                    }
                    else
                    {
                        <RadzenAlert AlertStyle="AlertStyle.Danger"
                                     Text="You are not authorized to access this resource."
                                     class="p-4 mb-4 text-red-800 bg-red-100 border border-red-200 rounded-lg" />
                    }
                </NotAuthorized>
            </AuthorizeRouteView>
        </Found>
        <NotFound>
            <PageTitle>Not found</PageTitle>
            <LayoutView Layout="@typeof(Barret.Web.Server.Features.Shared.Components.Layout.MainLayout)">
                <p role="alert">Sorry, there's nothing at this address.</p>
            </LayoutView>
        </NotFound>
    </Router>
</CascadingAuthenticationState>