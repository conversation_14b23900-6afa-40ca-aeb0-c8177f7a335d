<Project Sdk="Microsoft.NET.Sdk.Web">

  <PropertyGroup>
    <TargetFramework>net8.0</TargetFramework>
    <Nullable>enable</Nullable>
    <ImplicitUsings>enable</ImplicitUsings>
  </PropertyGroup>

  <ItemGroup>

    <PackageReference Include="FluentValidation" Version="11.11.0" />
    <PackageReference Include="Microsoft.AspNetCore.Identity.EntityFrameworkCore" Version="8.0.2" />
    <PackageReference Include="Microsoft.AspNetCore.Identity.UI" Version="8.0.2" />
    <PackageReference Include="Microsoft.EntityFrameworkCore.Design" Version="9.0.5">
      <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
      <PrivateAssets>all</PrivateAssets>
    </PackageReference>
    <PackageReference Include="Microsoft.EntityFrameworkCore.Sqlite" Version="9.0.3" />
    <PackageReference Include="Microsoft.EntityFrameworkCore.Tools" Version="9.0.3">
      <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
      <PrivateAssets>all</PrivateAssets>
    </PackageReference>
    <PackageReference Include="Microsoft.EntityFrameworkCore" Version="9.0.5" />
    <PackageReference Include="Radzen.Blazor" Version="7.0.7" />
    <PackageReference Include="ReactiveUI" Version="19.5.41" />
    <PackageReference Include="ReactiveUI.Blazor" Version="19.5.41" />
    <PackageReference Include="ReactiveUI.Fody" Version="19.5.41" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\Barret.Core\Barret.Core.csproj" />
    <ProjectReference Include="..\Barret.Services.Core\Barret.Services.Core.csproj" />
    <ProjectReference Include="..\Barret.Services\Barret.Services.csproj" />
    <ProjectReference Include="..\Barret.Shared\Barret.Shared.csproj" />
  </ItemGroup>

  <ItemGroup>
    <!-- Exclude the Test directory from compilation -->
    <Compile Remove="Test\**" />
    <Content Remove="Test\**" />
    <EmbeddedResource Remove="Test\**" />
    <None Remove="Test\**" />
  </ItemGroup>

  <!-- CSS Compilation Targets (Modern CSS Custom Properties Architecture) -->
  <PropertyGroup>
    <EnableCssCompilation Condition="'$(EnableCssCompilation)' == ''">true</EnableCssCompilation>
    <CssInputPath>wwwroot/css/Styles</CssInputPath>
    <CssOutputPath>wwwroot/css/Styles</CssOutputPath>
  </PropertyGroup>

  <!-- CSS Files to watch for changes -->
  <ItemGroup>
    <CssFiles Include="$(CssInputPath)/main.css" />
    <CssFiles Include="wwwroot/css/design-system/**/*.css" />
    <Watch Include="$(CssInputPath)/main.css" />
    <Watch Include="wwwroot/css/design-system/**/*.css" />
  </ItemGroup>

  <!-- Build Target: Compile CSS before build and run -->
  <Target Name="CompileCss" BeforeTargets="Build;Run" Condition="'$(EnableCssCompilation)' == 'true'">
    <Message Text="Checking CSS files for changes..." Importance="high" />

    <!-- Check if compiled CSS file exists -->
    <PropertyGroup>
      <DistCssExists Condition="Exists('$(CssOutputPath)/dist.css')">true</DistCssExists>
      <CssNeedsCompilation Condition="'$(DistCssExists)' != 'true'">true</CssNeedsCompilation>
    </PropertyGroup>

    <!-- Always compile in development for simplicity -->
    <PropertyGroup Condition="'$(Configuration)' == 'Debug'">
      <CssNeedsCompilation>true</CssNeedsCompilation>
    </PropertyGroup>

    <Message Text="CSS compilation needed: $(CssNeedsCompilation)" Importance="normal" />

    <!-- Compile CSS if needed -->
    <Exec Command="npm run build:css:dev"
          Condition="'$(CssNeedsCompilation)' == 'true'"
          ContinueOnError="false"
          WorkingDirectory="$(MSBuildProjectDirectory)/../" />

    <Message Text="CSS compilation completed."
             Condition="'$(CssNeedsCompilation)' == 'true'"
             Importance="high" />
  </Target>

  <!-- Development Target: Watch CSS files during development -->
  <Target Name="WatchCss" Condition="'$(DOTNET_RUNNING_IN_CONTAINER)' != 'true' AND '$(EnableCssCompilation)' == 'true'">
    <Message Text="Starting CSS watch mode..." Importance="high" />
    <Exec Command="npm run watch:css"
          ContinueOnError="true"
          WorkingDirectory="$(MSBuildProjectDirectory)/../" />
  </Target>

  <!-- Clean Target: Remove compiled CSS files -->
  <Target Name="CleanCss" BeforeTargets="Clean">
    <ItemGroup>
      <CompiledCssFiles Include="$(CssOutputPath)/dist.css" />
    </ItemGroup>
    <Delete Files="@(CompiledCssFiles)" ContinueOnError="true" />
  </Target>

</Project>
