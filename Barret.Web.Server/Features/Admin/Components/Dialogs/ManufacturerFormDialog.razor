@using Barret.Shared.DTOs.Devices
@using <PERSON><PERSON><PERSON>
@inject Radzen.DialogService DialogService

@* Manufacturer Form Dialog Component *@

<div class="dialog">
    <div class="dialog-header">
        <div class="flex items-center">
            <svg class="h-6 w-6 mr-3 text-blue-600" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                <rect width="7" height="7" x="3" y="3" rx="1"></rect>
                <rect width="7" height="7" x="14" y="3" rx="1"></rect>
                <rect width="7" height="7" x="14" y="14" rx="1"></rect>
                <rect width="7" height="7" x="3" y="14" rx="1"></rect>
            </svg>
            <h3 class="dialog-title">@Title</h3>
        </div>
    </div>

    <div class="dialog-content dialog-content-form">
        @if (HasError)
        {
            <div class="mb-4 p-3 bg-red-50 border border-red-200 rounded-md">
                <div class="flex items-center">
                    <i class="bi bi-exclamation-triangle text-red-500 mr-2"></i>
                    <span class="text-red-700 text-sm">@ErrorMessage</span>
                </div>
            </div>
        }

        <div class="form-group">
            <label for="manufacturerName" class="form-label">Manufacturer Name</label>
            <input type="text" id="manufacturerName"
                   class="form-input @(HasNameError ? "form-input-error" : "")"
                   @bind="ManufacturerName"
                   @bind:event="oninput"
                   placeholder="Enter manufacturer name" />
            @if (HasNameError)
            {
                <p class="form-error">@NameError</p>
            }
        </div>
    </div>

    <div class="dialog-footer">
        <div class="flex gap-2 justify-end">
            <RadzenButton Text="Cancel"
                          ButtonStyle="ButtonStyle.Secondary"
                          Click="@CancelAsync"
                          Disabled="@IsProcessing"
                          class="btn btn-md btn-secondary" />
            <RadzenButton Text="Save"
                          Icon="save"
                          ButtonStyle="ButtonStyle.Primary"
                          Click="@SaveAsync"
                          Disabled="@(!IsValid || IsProcessing)"
                          class="btn btn-md btn-primary">
                @if (IsProcessing)
                {
                    <i class="bi bi-arrow-clockwise animate-spin mr-2"></i>
                }
                Save
            </RadzenButton>
        </div>
    </div>
</div>

@code {
    [Parameter] public ManufacturerInfo? Manufacturer { get; set; }
    [Parameter] public bool IsEditing { get; set; } = false;

    private string manufacturerName = "";
    private string nameError = "";
    private string errorMessage = "";
    private bool isProcessing = false;

    public string Title => IsEditing ? "Edit Manufacturer" : "Add Manufacturer";
    public bool HasError => !string.IsNullOrEmpty(errorMessage);
    public string ErrorMessage => errorMessage;
    public bool HasNameError => !string.IsNullOrEmpty(nameError);
    public string NameError => nameError;
    public bool IsProcessing => isProcessing;
    public bool IsValid => !string.IsNullOrWhiteSpace(manufacturerName) && !HasNameError;

    public string ManufacturerName
    {
        get => manufacturerName;
        set
        {
            manufacturerName = value;
            ValidateName();
        }
    }

    protected override void OnInitialized()
    {
        if (Manufacturer != null)
        {
            manufacturerName = Manufacturer.Name ?? "";
        }
    }

    private void ValidateName()
    {
        nameError = "";
        
        if (string.IsNullOrWhiteSpace(manufacturerName))
        {
            nameError = "Manufacturer name is required.";
        }
        else if (manufacturerName.Length < 2)
        {
            nameError = "Manufacturer name must be at least 2 characters long.";
        }
        else if (manufacturerName.Length > 100)
        {
            nameError = "Manufacturer name cannot exceed 100 characters.";
        }
    }

    private Task SaveAsync()
    {
        try
        {
            isProcessing = true;
            errorMessage = "";

            ValidateName();

            if (!IsValid)
            {
                return Task.CompletedTask;
            }

            var result = new ManufacturerFormResult
            {
                Name = manufacturerName.Trim(),
                IsEditing = IsEditing,
                OriginalManufacturer = Manufacturer
            };

            DialogService.Close(result);
            return Task.CompletedTask;
        }
        catch (Exception ex)
        {
            errorMessage = $"Error saving manufacturer: {ex.Message}";
            return Task.CompletedTask;
        }
        finally
        {
            isProcessing = false;
        }
    }

    private Task CancelAsync()
    {
        DialogService.Close(null);
        return Task.CompletedTask;
    }

    public class ManufacturerFormResult
    {
        public string Name { get; set; } = "";
        public bool IsEditing { get; set; }
        public ManufacturerInfo? OriginalManufacturer { get; set; }
    }
}
