using Barret.Services.Core.Areas.Manufacturers;
using Barret.Shared.DTOs.Devices;
using Radzen.Blazor;
using Microsoft.AspNetCore.Components;
using Microsoft.Extensions.Logging;
using Barret.Web.Server.Services;
using Barret.Web.Server.Features.Admin.Components.Dialogs;
using Radzen;

namespace Barret.Web.Server.Features.Admin.Components
{
    /// <summary>
    /// Base class for the manufacturers manager view.
    /// </summary>
    public class ManufacturersManagerViewBase : ComponentBase
    {
        /// <summary>
        /// Gets or sets the title of the view.
        /// </summary>
        [Parameter]
        public string Title { get; set; } = "Manufacturers & Models";

        /// <summary>
        /// Gets or sets the manufacturer service.
        /// </summary>
        [Inject]
        protected IManufacturerService ManufacturerService { get; set; } = null!;

        /// <summary>
        /// Gets or sets the logger.
        /// </summary>
        [Inject]
        protected ILogger<ManufacturersManagerViewBase> Logger { get; set; } = null!;

        /// <summary>
        /// Gets or sets the toast notification service.
        /// </summary>
        [Inject]
        protected IBarretToastService ToastService { get; set; } = null!;

        /// <summary>
        /// Gets or sets the Radzen dialog service.
        /// </summary>
        [Inject]
        protected DialogService DialogService { get; set; } = null!;

        /// <summary>
        /// Gets or sets the grid reference.
        /// </summary>
        protected RadzenDataGrid<ManufacturerInfo> Grid { get; set; } = null!;

        /// <summary>
        /// Gets or sets a value indicating whether the component is loading.
        /// </summary>
        protected bool IsLoading { get; set; } = true;

        /// <summary>
        /// Gets or sets the list of manufacturers.
        /// </summary>
        protected List<ManufacturerInfo> Manufacturers { get; set; } = new();



        // No longer needed - using service-based dialogs

        /// <summary>
        /// Initializes the component.
        /// </summary>
        /// <returns>A task that represents the asynchronous operation.</returns>
        protected override async Task OnInitializedAsync()
        {
            await LoadManufacturersAsync();
        }

        /// <summary>
        /// Loads the manufacturers from the service.
        /// </summary>
        /// <returns>A task that represents the asynchronous operation.</returns>
        protected async Task LoadManufacturersAsync()
        {
            try
            {
                IsLoading = true;
                StateHasChanged();

                var result = await ManufacturerService.GetAllManufacturersAsync();
                if (result.Success)
                {
                    Manufacturers = result.Data.ToList();
                }
                else
                {
                    ToastService.ShowToast("Error", $"Failed to load manufacturers: {result.ErrorMessage}", ToastType.Error);
                    Logger.LogError("Failed to load manufacturers: {ErrorMessage}", result.ErrorMessage);
                }
            }
            catch (Exception ex)
            {
                ToastService.ShowToast("Error", $"An error occurred: {ex.Message}", ToastType.Error);
                Logger.LogError(ex, "Error loading manufacturers");
            }
            finally
            {
                IsLoading = false;
                StateHasChanged();
            }
        }



        /// <summary>
        /// Opens the add manufacturer dialog.
        /// </summary>
        protected async Task OpenAddManufacturerDialog()
        {
            var result = await DialogService.OpenAsync<ManufacturerFormDialog>(
                "Add Manufacturer",
                new Dictionary<string, object>()
                {
                    { "Manufacturer", null },
                    { "IsEditing", false }
                },
                new DialogOptions()
                {
                    Width = "500px",
                    CssClass = "dialog-md"
                });

            if (result is ManufacturerFormDialog.ManufacturerFormResult formResult)
            {
                await SaveManufacturer(formResult);
            }
        }

        /// <summary>
        /// Opens the edit manufacturer dialog.
        /// </summary>
        /// <param name="manufacturer">The manufacturer to edit.</param>
        protected async Task EditManufacturer(ManufacturerInfo manufacturer)
        {
            var result = await DialogService.OpenAsync<ManufacturerFormDialog>(
                "Edit Manufacturer",
                new Dictionary<string, object>()
                {
                    { "Manufacturer", manufacturer },
                    { "IsEditing", true }
                },
                new DialogOptions()
                {
                    Width = "500px",
                    CssClass = "dialog-md"
                });

            if (result is ManufacturerFormDialog.ManufacturerFormResult formResult)
            {
                await SaveManufacturer(formResult);
            }
        }

        /// <summary>
        /// Opens the delete manufacturer confirmation dialog.
        /// </summary>
        /// <param name="manufacturer">The manufacturer to delete.</param>
        protected async Task DeleteManufacturer(ManufacturerInfo manufacturer)
        {
            var result = await DialogService.Confirm(
                $"Are you sure you want to delete the manufacturer \"{manufacturer.Name}\"? This action cannot be undone and will also delete all associated device models.",
                "Confirm Deletion",
                new ConfirmOptions()
                {
                    OkButtonText = "Delete",
                    CancelButtonText = "Cancel",
                    Width = "450px",
                    CssClass = "barret-confirmation-dialog"
                });

            if (result == true)
            {
                await ConfirmDeleteManufacturer(manufacturer);
            }
        }

        /// <summary>
        /// Saves the manufacturer.
        /// </summary>
        /// <param name="formResult">The form result from the dialog.</param>
        /// <returns>A task that represents the asynchronous operation.</returns>
        protected async Task SaveManufacturer(ManufacturerFormDialog.ManufacturerFormResult formResult)
        {
            try
            {
                if (!formResult.IsEditing)
                {
                    // Create new manufacturer
                    var result = await ManufacturerService.CreateManufacturerAsync(formResult.Name);
                    if (result.Success)
                    {
                        ToastService.ShowToast("Success", "Manufacturer created successfully", ToastType.Success);
                        await LoadManufacturersAsync();
                    }
                    else
                    {
                        ToastService.ShowToast("Error", $"Failed to create manufacturer: {result.ErrorMessage}", ToastType.Error);
                        Logger.LogError("Failed to create manufacturer: {ErrorMessage}", result.ErrorMessage);
                    }
                }
                else if (formResult.OriginalManufacturer != null)
                {
                    // Update existing manufacturer
                    var result = await ManufacturerService.UpdateManufacturerAsync(formResult.OriginalManufacturer.Id, formResult.Name);
                    if (result.Success)
                    {
                        ToastService.ShowToast("Success", "Manufacturer updated successfully", ToastType.Success);
                        await LoadManufacturersAsync();
                    }
                    else
                    {
                        ToastService.ShowToast("Error", $"Failed to update manufacturer: {result.ErrorMessage}", ToastType.Error);
                        Logger.LogError("Failed to update manufacturer: {ErrorMessage}", result.ErrorMessage);
                    }
                }
            }
            catch (Exception ex)
            {
                ToastService.ShowToast("Error", $"An error occurred: {ex.Message}", ToastType.Error);
                Logger.LogError(ex, "Error saving manufacturer");
            }
        }

        /// <summary>
        /// Confirms the deletion of a manufacturer.
        /// </summary>
        /// <param name="manufacturer">The manufacturer to delete.</param>
        /// <returns>A task that represents the asynchronous operation.</returns>
        protected async Task ConfirmDeleteManufacturer(ManufacturerInfo manufacturer)
        {
            try
            {
                var result = await ManufacturerService.DeleteManufacturerAsync(manufacturer.Id);
                if (result.Success)
                {
                    ToastService.ShowToast("Success", "Manufacturer deleted successfully", ToastType.Success);
                    await LoadManufacturersAsync();
                }
                else
                {
                    ToastService.ShowToast("Error", $"Failed to delete manufacturer: {result.ErrorMessage}", ToastType.Error);
                    Logger.LogError("Failed to delete manufacturer: {ErrorMessage}", result.ErrorMessage);
                }
            }
            catch (Exception ex)
            {
                ToastService.ShowToast("Error", $"An error occurred: {ex.Message}", ToastType.Error);
                Logger.LogError(ex, "Error deleting manufacturer");
            }
        }
    }
}
