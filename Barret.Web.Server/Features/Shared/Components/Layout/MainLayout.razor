@using Microsoft.AspNetCore.Components.Web
@using Radzen.Blazor
@using Barret.Web.Server.Features.Shared.Components
@inherits LayoutComponentBase

<PageTitle>Barret - Vehicle Configurator</PageTitle>

@* Replaced RadzenTheme component with custom Material 3 theme CSS *@

<div class="min-h-screen flex flex-col" style="background-color: var(--rz-base-background-color);">
    <!-- Header with logo, theme toggle and user dropdown -->
    <header style="background-color: var(--rz-base-background-color); border-bottom: 1px solid var(--rz-border-color);">
        <div class="flex justify-between items-center px-6 py-4">
            <!-- Left side: Logo -->
            <div class="flex items-center">
                <img src="/seafar_logo_color_text.svg"
                     alt="Seafar Logo"
                     class="h-8 w-auto" />
            </div>

            <!-- Right side: Theme toggle and user menu -->
            <div class="flex items-center gap-4">
                <ThemeToggle @rendermode="RenderMode.InteractiveServer" />
                <Barret.Web.Server.Shared.LoginDisplay />
            </div>
        </div>
    </header>

    <!-- Content -->
    <main class="flex-1" style="background-color: var(--rz-base-background-color);">
        @Body
    </main>
</div>

<!-- Radzen Components - includes dialog support -->
<RadzenComponents @rendermode="RenderMode.InteractiveServer" />

<!-- Radzen Dialog Component - Required for DialogService -->
<RadzenDialog @rendermode="RenderMode.InteractiveServer" />

<!-- Radzen Notification Component - For notifications -->
<RadzenNotification @rendermode="RenderMode.InteractiveServer" />
