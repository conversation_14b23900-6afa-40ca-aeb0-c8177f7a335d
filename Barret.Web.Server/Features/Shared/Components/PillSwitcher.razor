@namespace Barret.Web.Server.Features.Shared.Components
@typeparam TValue where TValue : struct

<div class="inline-flex items-center bg-gray-100 rounded-full p-1 gap-1">
    @foreach (var option in Options)
    {
        <button type="button"
                class="@GetButtonClasses(option.Value)"
                aria-label="@option.Label"
                @onclick="@(() => HandleOptionClick(option.Value))">
            @if (!string.IsNullOrEmpty(option.Icon))
            {
                <RadzenIcon Icon="@option.Icon" class="h-4 w-4" />
            }
            @if (ShowLabels && !string.IsNullOrEmpty(option.Label))
            {
                <span class="@(string.IsNullOrEmpty(option.Icon) ? "" : "ml-1")">@option.Label</span>
            }
        </button>
    }
</div>

@code {
    [Parameter] public TValue Value { get; set; }
    [Parameter] public EventCallback<TValue> ValueChanged { get; set; }
    [Parameter] public List<PillSwitcherOption<TValue>> Options { get; set; } = new();
    [Parameter] public bool ShowLabels { get; set; } = false;
    [Parameter] public string Size { get; set; } = "medium"; // small, medium, large

    private async Task HandleOptionClick(TValue value)
    {
        if (!EqualityComparer<TValue>.Default.Equals(Value, value))
        {
            Value = value;
            await ValueChanged.InvokeAsync(value);
        }
    }

    private string GetButtonClasses(TValue optionValue)
    {
        var isSelected = EqualityComparer<TValue>.Default.Equals(Value, optionValue);
        var sizeClasses = Size switch
        {
            "small" => "h-6 px-2 text-xs",
            "large" => "h-10 px-4 text-base",
            _ => "h-8 px-3 text-sm"
        };

        var baseClasses = $"flex items-center justify-center rounded-full font-medium transition-all duration-200 {sizeClasses}";
        
        if (isSelected)
        {
            return $"{baseClasses} bg-white text-gray-900 shadow-sm";
        }
        else
        {
            return $"{baseClasses} text-gray-500 hover:text-gray-700 hover:bg-gray-50";
        }
    }
}

public class PillSwitcherOption<TValue> where TValue : struct
{
    public TValue Value { get; set; }
    public string Label { get; set; } = string.Empty;
    public string Icon { get; set; } = string.Empty;
}
