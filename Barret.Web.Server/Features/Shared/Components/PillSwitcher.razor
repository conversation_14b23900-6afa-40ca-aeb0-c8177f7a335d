@namespace Barret.Web.Server.Features.Shared.Components
@typeparam TValue where TValue : struct

<div class="barret-pill-switcher @GetSizeClass()">
    @foreach (var option in Options)
    {
        <button type="button"
                class="@GetButtonClasses(option.Value)"
                aria-label="@option.Label"
                @onclick="@(() => HandleOptionClick(option.Value))">
            @if (!string.IsNullOrEmpty(option.Icon))
            {
                <RadzenIcon Icon="@option.Icon" class="h-4 w-4" />
            }
            @if (ShowLabels && !string.IsNullOrEmpty(option.Label))
            {
                <span>@option.Label</span>
            }
        </button>
    }
</div>

@code {
    [Parameter] public TValue Value { get; set; }
    [Parameter] public EventCallback<TValue> ValueChanged { get; set; }
    [Parameter] public List<PillSwitcherOption<TValue>> Options { get; set; } = new();
    [Parameter] public bool ShowLabels { get; set; } = false;
    [Parameter] public string Size { get; set; } = "medium"; // small, medium, large

    private async Task HandleOptionClick(TValue value)
    {
        if (!EqualityComparer<TValue>.Default.Equals(Value, value))
        {
            Value = value;
            await ValueChanged.InvokeAsync(value);
        }
    }

    private string GetSizeClass()
    {
        return Size switch
        {
            "small" => "size-small",
            "large" => "size-large",
            _ => ""
        };
    }

    private string GetButtonClasses(TValue optionValue)
    {
        var isSelected = EqualityComparer<TValue>.Default.Equals(Value, optionValue);
        return isSelected ? "selected" : "";
    }
}

public class PillSwitcherOption<TValue> where TValue : struct
{
    public TValue Value { get; set; }
    public string Label { get; set; } = string.Empty;
    public string Icon { get; set; } = string.Empty;
}
