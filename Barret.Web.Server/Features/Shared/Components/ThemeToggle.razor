@namespace Barret.Web.Server.Features.Shared.Components
@using Barret.Web.Server.Services
@inject Barret.Web.Server.Services.ThemeService ThemeService
@implements IDisposable

<div class="flex items-center gap-2">
    <RadzenText TextStyle="TextStyle.Body2" class="text-sm font-medium">Theme:</RadzenText>
    <RadzenButton Text="@(ThemeService.CurrentTheme == "light" ? "🌙 Dark" : "☀️ Light")"
                  ButtonStyle="@(ThemeService.CurrentTheme == "light" ? ButtonStyle.Light : ButtonStyle.Dark)"
                  Size="ButtonSize.ExtraSmall"
                  Click="ToggleTheme"
                  class="px-3 py-1 text-xs font-medium rounded-full" />
</div>

@code {
    private bool _isInitialized = false;

    protected override void OnInitialized()
    {
        // Subscribe to theme changes
        ThemeService.ThemeChanged += OnThemeChanged;
    }

    protected override async Task OnAfterRenderAsync(bool firstRender)
    {
        if (firstRender && !_isInitialized)
        {
            // Initialize theme after first render when JavaScript is available
            await ThemeService.InitializeThemeAsync();
            _isInitialized = true;
            StateHasChanged();
        }
    }

    private async Task ToggleTheme()
    {
        await ThemeService.ToggleThemeAsync();
    }

    private void OnThemeChanged(string newTheme)
    {
        // Re-render component when theme changes
        InvokeAsync(StateHasChanged);
    }

    public void Dispose()
    {
        ThemeService.ThemeChanged -= OnThemeChanged;
    }
}
