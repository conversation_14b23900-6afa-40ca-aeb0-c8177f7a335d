@namespace Barret.Web.Server.Features.Shared.Components
@using Barret.Web.Server.Services
@inject Barret.Web.Server.Services.ThemeService ThemeService
@implements IDisposable

<PillSwitcher TValue="ThemeMode"
              Value="@CurrentThemeMode"
              ValueChanged="@HandleThemeChange"
              Options="@ThemeOptions"
              Size="small" />

@code {
    public enum ThemeMode
    {
        Light,
        Dark
    }

    private ThemeMode CurrentThemeMode => ThemeService.CurrentTheme == "dark" ? ThemeMode.Dark : ThemeMode.Light;

    private List<PillSwitcher<ThemeMode>.PillSwitcherOption<ThemeMode>> ThemeOptions = new()
    {
        new() { Value = ThemeMode.Light, Icon = "light_mode", Label = "Light" },
        new() { Value = ThemeMode.Dark, Icon = "dark_mode", Label = "Dark" }
    };

    private async Task HandleThemeChange(ThemeMode mode)
    {
        if (mode == ThemeMode.Dark && ThemeService.CurrentTheme != "dark")
        {
            await ThemeService.SetThemeAsync("dark");
        }
        else if (mode == ThemeMode.Light && ThemeService.CurrentTheme != "light")
        {
            await ThemeService.SetThemeAsync("light");
        }
    }
    private bool _isInitialized = false;

    protected override void OnInitialized()
    {
        // Subscribe to theme changes
        ThemeService.ThemeChanged += OnThemeChanged;
    }

    protected override async Task OnAfterRenderAsync(bool firstRender)
    {
        if (firstRender && !_isInitialized)
        {
            // Initialize theme after first render when JavaScript is available
            await ThemeService.InitializeThemeAsync();
            _isInitialized = true;
            StateHasChanged();
        }
    }

    private async Task ToggleTheme()
    {
        await ThemeService.ToggleThemeAsync();
    }

    private void OnThemeChanged(string newTheme)
    {
        // Re-render component when theme changes
        InvokeAsync(StateHasChanged);
    }

    public void Dispose()
    {
        ThemeService.ThemeChanged -= OnThemeChanged;
    }
}
