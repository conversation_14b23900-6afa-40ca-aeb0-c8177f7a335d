@using Barret.Core.Areas.Devices.Enums
@using Barret.Shared.DTOs.Devices
@using Barret.Shared.DTOs.Devices.Alarms
@using Barret.Web.Server.Features.Vehicles.Editor.Components.Devices.ViewModels
@inherits Barret.Web.Server.Features.Shared.ViewBase<AlarmManagerViewModel>

<div class="alarms-section">
    <div class="flex justify-between items-center mb-4">
        <h5 class="text-lg font-semibold text-gray-900 mb-0">Device Alarms</h5>
        <RadzenButton Icon="add"
                     Text="Add Alarm"
                     ButtonStyle="ButtonStyle.Primary"
                     Size="ButtonSize.Small"
                     Click="@(() => ViewModel.AddAlarmCommand.Execute())"
                     class="btn btn-md btn-primary" />
    </div>

    @if (ViewModel.Alarms?.Any() == true)
    {
        <div class="space-y-3">
            @foreach (var alarm in ViewModel.Alarms.Select((alarm, index) => new { alarm, index }))
            {
                <div class="bg-white border border-gray-200 rounded-lg overflow-hidden">
                    <div class="bg-gray-50 px-4 py-3 border-b border-gray-200">
                        <div class="flex justify-between items-center">
                            <div class="flex items-center space-x-3">
                                <span class="font-medium text-gray-900">
                                    @(string.IsNullOrEmpty(alarm.alarm.Description) ? "New Alarm" : alarm.alarm.Description)
                                </span>
                                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium @ViewModel.GetBadgeClass(alarm.alarm.NotificationType) text-white">
                                    @ViewModel.GetNotificationTypeText(alarm.alarm.NotificationType)
                                </span>
                            </div>
                            <RadzenButton Icon="delete"
                                         ButtonStyle="ButtonStyle.Danger"
                                         Variant="Variant.Outlined"
                                         Size="ButtonSize.ExtraSmall"
                                         Click="@(() => ViewModel.RemoveAlarmCommand.Execute(alarm.alarm))"
                                         title="Remove Alarm"
                                         class="barret-btn barret-action-btn" />
                        </div>
                    </div>
                    
                    <div class="p-4 space-y-4">
                        <!-- Description Field -->
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                            <RadzenFormField Text="Description" Variant="Variant.Outlined">
                                <RadzenTextBox @bind-Value="@alarm.alarm.Description"
                                             Placeholder="Enter alarm description..."
                                             Change="@(() => ViewModel.UpdateAlarmProperty(alarm.alarm, nameof(AlarmDto.Description), alarm.alarm.Description))"
                                             class="form-input w-full" />
                            </RadzenFormField>
                            
                            <RadzenFormField Text="Message" Variant="Variant.Outlined">
                                <RadzenTextBox @bind-Value="@alarm.alarm.Message"
                                             Placeholder="Enter alarm message..."
                                             Change="@(() => ViewModel.UpdateAlarmProperty(alarm.alarm, nameof(AlarmDto.Message), alarm.alarm.Message))"
                                             class="form-input w-full" />
                            </RadzenFormField>
                        </div>

                        <!-- Notification Type and Group -->
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                            <RadzenFormField Text="Notification Type" Variant="Variant.Outlined">
                                <RadzenDropDown @bind-Value="@alarm.alarm.NotificationType"
                                              Data="@GetNotificationTypes()"
                                              Change="@(args => ViewModel.UpdateAlarmProperty(alarm.alarm, nameof(AlarmDto.NotificationType), args))"
                                              class="form-input w-full" />
                            </RadzenFormField>

                            <RadzenFormField Text="Notification Group" Variant="Variant.Outlined">
                                <RadzenDropDown @bind-Value="@alarm.alarm.NotificationGroupId"
                                              Data="@GetNotificationGroupIds()"
                                              Change="@(args => ViewModel.UpdateAlarmProperty(alarm.alarm, nameof(AlarmDto.NotificationGroupId), args))"
                                              class="form-input w-full" />
                            </RadzenFormField>
                        </div>

                        <!-- Warning and Domain Driver IDs -->
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                            <RadzenFormField Text="Warning ID" Variant="Variant.Outlined">
                                <RadzenDropDown @bind-Value="@alarm.alarm.WarningId"
                                              Data="@GetWarningIds()"
                                              Change="@(args => ViewModel.UpdateAlarmProperty(alarm.alarm, nameof(AlarmDto.WarningId), args))"
                                              class="form-input w-full" />
                            </RadzenFormField>

                            <RadzenFormField Text="Domain Driver ID" Variant="Variant.Outlined">
                                <RadzenDropDown @bind-Value="@alarm.alarm.WarningId"
                                              Data="@GetWarningIds()"
                                              Change="@(args => ViewModel.UpdateAlarmProperty(alarm.alarm, nameof(AlarmDto.WarningId), args))"
                                              class="form-input w-full" />
                            </RadzenFormField>
                        </div>
                    </div>
                </div>
            }
        </div>
    }
    else
    {
        <div class="bg-blue-50 border border-blue-200 rounded-lg p-4">
            <div class="flex items-center">
                <i class="bi bi-info-circle text-blue-600 mr-3"></i>
                <span class="text-blue-800">No alarms configured for this device. Click "Add Alarm" to create one.</span>
            </div>
        </div>
    }
</div>
