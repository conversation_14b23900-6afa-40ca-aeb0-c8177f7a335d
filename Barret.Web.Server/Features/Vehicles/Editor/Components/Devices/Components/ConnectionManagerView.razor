@using Barret.Shared.DTOs.Devices
@using Barret.Web.Server.Extensions
@using Barret.Web.Server.Features.Vehicles.Editor.Components.Devices.ViewModels
@inherits Barret.Web.Server.Features.Shared.ViewBase<ConnectionManagerViewModel>
@inject DialogService DialogService

<div class="connections-section">
    <div class="flex justify-between items-center mb-4">
        <h6 class="text-base font-semibold text-gray-900 mb-0">Device Connections</h6>
        <RadzenButton Icon="add" 
                     Text="Add Connection" 
                     ButtonStyle="ButtonStyle.Primary"
                     Size="ButtonSize.Small"
                     Click="@(() => ViewModel.AddConnectionCommand.Execute())"
                     class="barret-btn barret-form-btn" />
    </div>

    @if (ViewModel.Connections?.Any() == true)
    {
        <RadzenDataGrid @ref="connectionsGrid"
                       Data="@ViewModel.Connections"
                       TItem="DeviceConnectionDto"
                       AllowFiltering="true"
                       AllowSorting="true"
                       AllowPaging="true"
                       PageSize="10"
                       PagerHorizontalAlign="HorizontalAlign.Left"
                       ShowPagingSummary="true"
                       class="barret-grid barret-grid-compact">
            
            <Columns>
                <RadzenDataGridColumn TItem="DeviceConnectionDto"
                                    Property="Type"
                                    Title="Connection Name"
                                    Width="200px">
                    <Template Context="connection">
                        <span class="font-medium text-gray-900">@connection.ConnectionName()</span>
                    </Template>
                </RadzenDataGridColumn>

                <RadzenDataGridColumn TItem="DeviceConnectionDto"
                                    Property="InterfaceDeviceId"
                                    Title="Target Device"
                                    Width="200px">
                    <Template Context="connection">
                        @{
                            var targetDevice = connection.TargetDevice(ViewModel.AllDevices ?? new List<DeviceDto>());
                        }
                        @if (targetDevice != null)
                        {
                            <span class="text-gray-700">@targetDevice.Name</span>
                        }
                        else
                        {
                            <span class="text-gray-400 italic">No target device</span>
                        }
                    </Template>
                </RadzenDataGridColumn>

                <RadzenDataGridColumn TItem="DeviceConnectionDto" 
                                    Property="ConnectionType" 
                                    Title="Type"
                                    Width="120px">
                    <Template Context="connection">
                        <span class="text-sm text-gray-600">@ViewModel.GetConnectionTypeText(connection)</span>
                    </Template>
                </RadzenDataGridColumn>

                <RadzenDataGridColumn TItem="DeviceConnectionDto" 
                                    Property="IsActive" 
                                    Title="Status"
                                    Width="100px">
                    <Template Context="connection">
                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium @ViewModel.GetConnectionStatusBadgeClass(connection)">
                            @ViewModel.GetConnectionStatusText(connection)
                        </span>
                    </Template>
                </RadzenDataGridColumn>

                <RadzenDataGridColumn TItem="DeviceConnectionDto" 
                                    Title="Actions" 
                                    Sortable="false" 
                                    Filterable="false"
                                    Width="100px">
                    <Template Context="connection">
                        <div class="flex space-x-2">
                            <RadzenButton Icon="edit"
                                         ButtonStyle="ButtonStyle.Light"
                                         Variant="Variant.Outlined"
                                         Size="ButtonSize.ExtraSmall"
                                         Click="@(() => EditConnection(connection))"
                                         title="Edit Connection"
                                         class="barret-btn barret-action-btn" />
                            <RadzenButton Icon="delete"
                                         ButtonStyle="ButtonStyle.Danger"
                                         Variant="Variant.Outlined"
                                         Size="ButtonSize.ExtraSmall"
                                         Click="@(() => ViewModel.RemoveConnectionCommand.Execute(connection))"
                                         title="Remove Connection"
                                         class="barret-btn barret-action-btn" />
                        </div>
                    </Template>
                </RadzenDataGridColumn>
            </Columns>
        </RadzenDataGrid>
    }
    else
    {
        <div class="bg-blue-50 border border-blue-200 rounded-lg p-4">
            <div class="flex items-center">
                <i class="bi bi-info-circle text-blue-600 mr-3"></i>
                <span class="text-blue-800">No connections configured for this device. Click "Add Connection" to create one.</span>
            </div>
        </div>
    }
</div>

@* Connection Edit Dialog *@
@if (ViewModel.IsAddConnectionDialogVisible && ViewModel.SelectedConnection != null)
{
    <RadzenDialog @bind-Visible="@ViewModel.IsAddConnectionDialogVisible">
        <div class="p-6 min-w-[500px]">
            <h4 class="text-lg font-semibold text-gray-900 mb-4">
                @(ViewModel.Connections.Contains(ViewModel.SelectedConnection) ? "Edit Connection" : "Add New Connection")
            </h4>
            
            <div class="space-y-4">
                <RadzenFormField Text="Connection Name" Variant="Variant.Outlined">
                    <RadzenTextBox @bind-Value="@ViewModel.SelectedConnectionName"
                                 Placeholder="Enter connection name..."
                                 class="form-input w-full" />
                </RadzenFormField>

                <RadzenFormField Text="Description" Variant="Variant.Outlined">
                    <RadzenTextArea @bind-Value="@ViewModel.SelectedConnectionDescription"
                                  Placeholder="Enter connection description..."
                                  Rows="3"
                                  class="form-input w-full" />
                </RadzenFormField>

                <div class="flex items-center space-x-2">
                    <RadzenCheckBox @bind-Value="@ViewModel.SelectedConnectionIsActive"
                                   Name="isActive" />
                    <RadzenLabel Text="Active Connection" Component="isActive" class="text-sm text-gray-700" />
                </div>
            </div>

            <div class="flex justify-end space-x-3 mt-6 pt-4 border-t border-gray-200">
                <RadzenButton Text="Cancel" 
                             ButtonStyle="ButtonStyle.Light"
                             Click="@(() => ViewModel.CancelConnectionCommand.Execute())"
                             class="barret-btn barret-secondary-btn" />
                <RadzenButton Text="Save" 
                             ButtonStyle="ButtonStyle.Primary"
                             Click="@(() => ViewModel.SaveConnectionCommand.Execute())"
                             class="barret-btn barret-primary-btn" />
            </div>
        </div>
    </RadzenDialog>
}
