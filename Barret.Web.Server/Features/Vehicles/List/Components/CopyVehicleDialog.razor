@using <PERSON><PERSON><PERSON>
@using Radzen.Blazor
@inject Radzen.DialogService DialogService

@* Copy Vehicle Dialog - Pure Radzen + Design System Implementation *@

<div class="dialog">
    <div class="dialog-content">
        <div class="flex flex-col items-center text-center mb-6">
            <div class="dialog-icon dialog-icon-info mb-4">
                <i class="bi bi-copy text-2xl"></i>
            </div>
            <h3 class="dialog-title mb-4">Copy Vehicle</h3>
            <p class="text-gray-600 mb-6">Please enter a name for the copied vehicle:</p>
        </div>

        <div class="space-y-4">
            <div class="form-group">
                <RadzenLabel Text="Vehicle Name" class="form-label" />
                <RadzenTextBox @bind-Value="@VehicleName"
                              Placeholder="Enter vehicle name"
                              class="form-input w-full" />
            </div>
        </div>
    </div>

    <div class="dialog-footer">
        <RadzenButton Text="Cancel"
                     ButtonStyle="ButtonStyle.Secondary"
                     Click="@CancelAsync"
                     class="barret-btn-secondary" />
        <RadzenButton Text="@GetCopyButtonText()"
                     ButtonStyle="ButtonStyle.Primary"
                     Click="@CopyAsync"
                     Disabled="@(string.IsNullOrWhiteSpace(VehicleName) || IsProcessing)"
                     class="barret-btn-primary">
            @if (IsProcessing)
            {
                <i class="bi bi-arrow-clockwise animate-spin mr-2"></i>
            }
            @if (!IsProcessing)
            {
                <span>Copy</span>
            }
        </RadzenButton>
    </div>
</div>
