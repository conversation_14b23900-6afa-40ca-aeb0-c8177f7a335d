@using Barret.Web.Server.Features.Vehicles.Data

<RadzenCard class="group relative h-full card-with-shadow cursor-pointer" @onclick="() => OnConfigureClick.InvokeAsync(Vehicle.Id)">
    <!-- Image Section -->
    <div class="relative h-48 bg-gray-100 overflow-hidden rounded-t-xl">
        <img src="/images/seafar_placeholder_grey.svg"
             alt="@Vehicle.Name"
             class="w-full h-full object-cover transform group-hover:scale-105 transition-transform duration-500" />

        <div class="absolute top-3 right-3 bg-white/90 backdrop-blur-sm rounded-full px-3 py-1 text-sm font-medium text-gray-700">
            @Vehicle.DeviceCount Devices
        </div>
    </div>

    <!-- Content Section -->
    <div class="px-4 py-3">
        <!-- Header -->
        <div class="flex justify-between items-center mb-1">
            <h4 class="text-lg font-medium text-gray-900 group-hover:text-gray-700 transition-colors duration-200">
                @Vehicle.Name
            </h4>
            <div class="bg-gray-100 rounded-full p-1.5 group-hover:bg-gray-200 transition-colors duration-200" @onclick:stopPropagation="true">
                <RadzenIcon Icon="@Vehicle.IconCssClass" class="h-4 w-4 text-gray-700" />
            </div>
        </div>

        <!-- Description -->
        <p class="text-sm text-gray-500">@Vehicle.VehicleId</p>

        <!-- Footer -->
        <div class="flex justify-between items-center mt-3">
            <div class="flex items-center gap-2" @onclick:stopPropagation="true">
                <RadzenButton ButtonStyle="ButtonStyle.Light"
                             Size="ButtonSize.Small"
                             Icon="content_copy"
                             title="Copy"
                             class="barret-btn-secondary !h-8 !w-8 !p-0 !rounded-full"
                             Click="@(() => OnCopyClick.InvokeAsync(Vehicle.Id))" />
                <RadzenButton ButtonStyle="ButtonStyle.Light"
                             Size="ButtonSize.Small"
                             Icon="delete"
                             title="Delete"
                             class="barret-btn-secondary !h-8 !w-8 !p-0 !rounded-full"
                             Click="@(() => OnDeleteClick.InvokeAsync(Vehicle.Id))" />
            </div>
            <RadzenButton ButtonStyle="ButtonStyle.Light"
                         Size="ButtonSize.Small"
                         Text="Configure"
                         Icon="arrow_forward"
                         class="barret-btn-secondary !h-8 !px-3 !rounded-full !text-sm"
                         Click="@(() => OnConfigureClick.InvokeAsync(Vehicle.Id))"
                         @onclick:stopPropagation="true" />
        </div>
    </div>
</RadzenCard>
