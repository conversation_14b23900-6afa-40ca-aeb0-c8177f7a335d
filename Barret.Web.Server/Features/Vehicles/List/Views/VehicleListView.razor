@page "/vehicles"
@inherits VehicleListViewBase
@using Barret.Web.Server.Features.Vehicles.Data
@using Barret.Web.Server.Features.Vehicles.List.Components
@using Barret.Web.Server.Shared.Components.Dialogs
@using Radzen.Blazor

<div class="min-h-screen bg-white">
    <div class="max-w-[1400px] mx-auto px-6 py-8">
        <!-- Header -->
        <header class="mb-8">
            <div class="flex items-center justify-between mb-4">
                <h1 class="text-2xl font-medium text-gray-900">Vehicle Configurations</h1>
            </div>
            <div class="flex flex-col sm:flex-row sm:items-center justify-between gap-4">
                <p class="text-gray-500">Select a vehicle to configure or add a new one</p>
                <div class="relative w-full sm:w-64 md:w-80">
                    <input type="search"
                           placeholder="Search configurations..."
                           class="pl-10 h-10 rounded-full border-gray-200 focus:border-gray-300 focus:ring focus:ring-gray-200 focus:ring-opacity-50 w-full"
                           @bind="@ViewModel.Filter.SearchTerm"
                           @bind:event="oninput"
                           @onkeyup="HandleKeyUp" />
                    <div class="absolute left-3 top-1/2 -translate-y-1/2 text-gray-400">
                        <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                            <circle cx="11" cy="11" r="8" />
                            <path d="m21 21-4.3-4.3" />
                        </svg>
                    </div>
                </div>
            </div>
        </header>

        @if (ViewModel.HasError)
        {
            <div class="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-lg mb-6 flex justify-between items-center" role="alert">
                <div>
                    <span class="font-medium">Error:</span> @ViewModel.ErrorMessage
                </div>
                <button type="button" class="text-red-700 hover:text-red-900" @onclick="ClearError" aria-label="Close">
                    <svg class="h-5 w-5" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                        <line x1="18" y1="6" x2="6" y2="18"></line>
                        <line x1="6" y1="6" x2="18" y2="18"></line>
                    </svg>
                </button>
            </div>
        }

        <!-- Tabs Navigation -->
        <CustomTabControl
            ActiveTabIndex="@activeTabIndex"
            ActiveTabIndexChanged="@((value) => activeTabIndex = value)"
            OnTabClicked="HandleTabChangeAsync" />

        <!-- Content Header with View Toggle -->
        <div class="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4 mb-6">
            <div class="flex items-center gap-2">
                <h3 class="text-lg font-medium text-gray-900">
                    @{
                        var tabText = activeTabIndex switch
                        {
                            0 => "All Vehicles",
                            1 => "Vessels",
                            2 => "Trucks",
                            3 => "Cranes",
                            _ => "All Vehicles"
                        };
                    }
                    @tabText (@ViewModel.Vehicles.Count)
                </h3>

                <!-- View Toggle Buttons -->
                <div class="flex items-center ml-4 bg-gray-100 rounded-full p-1">
                    <button
                        class="flex items-center justify-center h-8 w-8 rounded-full @(ViewModel.ViewMode == ViewModeEnum.Grid ? "bg-white text-gray-900 shadow-sm" : "text-gray-500 hover:text-gray-700")"
                        aria-label="Grid view"
                        @onclick="@(() => ViewModel.ViewMode = ViewModeEnum.Grid)">
                        <svg class="h-4 w-4" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                            <rect width="7" height="7" x="3" y="3" rx="1"></rect>
                            <rect width="7" height="7" x="14" y="3" rx="1"></rect>
                            <rect width="7" height="7" x="14" y="14" rx="1"></rect>
                            <rect width="7" height="7" x="3" y="14" rx="1"></rect>
                        </svg>
                    </button>
                    <button
                        class="flex items-center justify-center h-8 w-8 rounded-full @(ViewModel.ViewMode == ViewModeEnum.List ? "bg-white text-gray-900 shadow-sm" : "text-gray-500 hover:text-gray-700")"
                        aria-label="List view"
                        @onclick="@(() => ViewModel.ViewMode = ViewModeEnum.List)">
                        <svg class="h-4 w-4" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                            <line x1="8" y1="6" x2="21" y2="6"></line>
                            <line x1="8" y1="12" x2="21" y2="12"></line>
                            <line x1="8" y1="18" x2="21" y2="18"></line>
                            <line x1="3" y1="6" x2="3.01" y2="6"></line>
                            <line x1="3" y1="12" x2="3.01" y2="12"></line>
                            <line x1="3" y1="18" x2="3.01" y2="18"></line>
                        </svg>
                    </button>
                </div>
            </div>
            <button @onclick="NavigateToCreateNew"
                   class="flex items-center gap-2 h-10 px-4 rounded-full bg-gray-900 text-white hover:bg-gray-800 transition-colors">
                <i class="bi bi-plus-circle"></i>
                Add New Vehicle
            </button>
        </div>

        @if (ViewModel.IsLoading)
        {
            <div class="flex justify-center items-center my-12">
                <div class="animate-spin h-8 w-8 border-4 border-gray-200 border-t-gray-800 rounded-full"></div>
                <p class="ml-3 text-gray-700">Loading vehicles...</p>
            </div>
        }
        else if (ViewModel.Vehicles.Count == 0)
        {
            <div class="text-center py-16 bg-gray-50 rounded-xl">
                <div class="mx-auto w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mb-4">
                    <svg class="h-8 w-8 text-gray-700" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                        <path d="M20 21c-3.5 0-7-1.5-7-1.5s-3.5 1.5-7 1.5c-2 0-3.5-.5-3.5-.5L2 12l.5-1C3 11 4.5 10 7 10c3.5 0 7 1.5 7 1.5s3.5-1.5 7-1.5c2.5 0 4 1 4.5 1l.5 1-1 9c-.5 0-2 .5-4 .5Z"></path>
                        <path d="M12 10V3l-3 2"></path>
                    </svg>
                </div>
                <h3 class="text-xl font-medium text-gray-900 mb-2">No Vehicles Found</h3>
                <p class="text-gray-500 mb-6 max-w-md mx-auto">
                    @if (!string.IsNullOrEmpty(ViewModel.Filter.SearchTerm) || (ViewModel.Filter.VehicleType.HasValue && ViewModel.Filter.VehicleType.Value != VehicleTypeEnum.All))
                    {
                        <span>No vehicles match your current filters. Try adjusting your search criteria.</span>
                    }
                    else
                    {
                        <span>You haven't added any vehicles yet. Add your first vehicle to get started with configuration.</span>
                    }
                </p>
                @if (string.IsNullOrEmpty(ViewModel.Filter.SearchTerm) && (!ViewModel.Filter.VehicleType.HasValue || ViewModel.Filter.VehicleType.Value == VehicleTypeEnum.All))
                {
                    <button @onclick="NavigateToCreateNew"
                           class="flex items-center gap-2 h-10 px-4 rounded-full bg-gray-900 text-white hover:bg-gray-800 transition-colors mx-auto">
                        <i class="bi bi-plus-circle"></i>
                        Add New Vehicle
                    </button>
                }
            </div>
        }
        else
        {
            @if (ViewModel.ViewMode == ViewModeEnum.Grid)
            {
                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                    @foreach (var vehicle in ViewModel.Vehicles)
                    {
                        <div>
                            <VehicleCard Vehicle="vehicle"
                                        OnConfigureClick="NavigateToVehicleConfiguration"
                                        OnCopyClick="HandleCopyVehicleAsync"
                                        OnDeleteClick="HandleDeleteVehicleAsync" />
                        </div>
                    }
                </div>
            }
            else
            {
                <RadzenDataGrid @ref="vehicleGrid"
                                Data="@ViewModel.Vehicles"
                                TItem="VehicleData"
                                AllowFiltering="true"
                                AllowSorting="true"
                                AllowPaging="true"
                                PageSize="10"
                                RowSelect="@OnVehicleRowSelect">
                    <Columns>
                        <RadzenDataGridColumn TItem="VehicleData" Property="Name" Title="Name" Width="250px">
                            <Template Context="vehicle">
                                <div class="flex items-center gap-3">
                                    <div class="bg-gray-100 rounded-full p-1.5 flex-shrink-0">
                                        <i class="@vehicle.IconCssClass text-gray-700"></i>
                                    </div>
                                    <span class="font-medium">@vehicle.Name</span>
                                </div>
                            </Template>
                        </RadzenDataGridColumn>

                        <RadzenDataGridColumn TItem="VehicleData" Property="VehicleId" Title="Vehicle ID" Width="150px" />

                        <RadzenDataGridColumn TItem="VehicleData" Property="VehicleType" Title="Type" Width="120px">
                            <Template Context="vehicle">
                                <span class="inline-flex items-center rounded-full border border-gray-200 bg-gray-50 px-2.5 py-0.5 text-xs font-medium text-gray-700">
                                    @vehicle.VehicleType
                                </span>
                            </Template>
                        </RadzenDataGridColumn>

                        <RadzenDataGridColumn TItem="VehicleData" Property="DeviceCount" Title="Devices" Width="120px">
                            <Template Context="vehicle">
                                <span class="flex items-center gap-1">
                                    <span class="h-2 w-2 rounded-full bg-green-500"></span>
                                    @vehicle.DeviceCount devices
                                </span>
                            </Template>
                        </RadzenDataGridColumn>

                        <RadzenDataGridColumn TItem="VehicleData" Title="Properties" Width="200px" Sortable="false" Filterable="false">
                            <Template Context="vehicle">
                                <div class="flex flex-wrap gap-2">
                                    @foreach (var prop in vehicle.Properties.Take(2))
                                    {
                                        <span class="text-sm">
                                            <span class="font-medium">@prop.Key:</span>
                                            <span class="text-gray-500">@prop.Value</span>
                                        </span>
                                    }
                                    @if (vehicle.Properties.Count > 2)
                                    {
                                        <span class="text-sm text-gray-500">
                                            +@(vehicle.Properties.Count - 2) more
                                        </span>
                                    }
                                </div>
                            </Template>
                        </RadzenDataGridColumn>

                        <RadzenDataGridColumn TItem="VehicleData" Title="Actions" Width="150px" Sortable="false" Filterable="false">
                            <Template Context="vehicle">
                                <div class="barret-grid-actions" @onclick:stopPropagation="true">
                                    <button @onclick="@(() => NavigateToVehicleConfiguration(vehicle.Id))"
                                           title="Configure"
                                           class="text-gray-700 hover:text-gray-900 hover:bg-gray-100 rounded-full h-8 w-8 p-0 flex items-center justify-center mr-1">
                                        <i class="bi bi-gear"></i>
                                    </button>
                                    <button @onclick="@(() => HandleCopyVehicleAsync(vehicle.Id))"
                                           title="Copy"
                                           class="text-gray-700 hover:text-gray-900 hover:bg-gray-100 rounded-full h-8 w-8 p-0 flex items-center justify-center mr-1">
                                        <i class="bi bi-copy"></i>
                                    </button>
                                    <button @onclick="@(() => HandleDeleteVehicleAsync(vehicle.Id))"
                                           title="Delete"
                                           class="text-gray-700 hover:text-gray-900 hover:bg-gray-100 rounded-full h-8 w-8 p-0 flex items-center justify-center">
                                        <i class="bi bi-trash"></i>
                                    </button>
                                </div>
                            </Template>
                        </RadzenDataGridColumn>
                    </Columns>
                </RadzenDataGrid>
            }
        }
    </div>
</div>

@* Delete confirmation and copy dialog are now handled via DialogService in HandleDeleteVehicleAsync and HandleCopyVehicleAsync *@