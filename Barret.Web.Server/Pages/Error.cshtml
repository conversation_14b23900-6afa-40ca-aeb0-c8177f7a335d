@page
@model Barret.Web.Server.Pages.ErrorModel

<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no" />
    <title>Error</title>
    <!-- Barret Light Theme (Complete Radzen Material + Design System) -->
    <link href="css/themes/radzen-barret.css" rel="stylesheet" />
    <!-- Tailwind CSS with integrated design system -->
    <link href="css/Styles/dist.css" rel="stylesheet" />
</head>

<body>
    <div class="min-h-screen flex items-center justify-center bg-gray-50">
        <div class="max-w-md w-full bg-white rounded-lg shadow-md p-6">
            <h1 class="text-2xl font-bold text-red-600 mb-4">Error.</h1>
            <h2 class="text-lg text-red-600 mb-6">An error occurred while processing your request.</h2>

            @if (Model.ShowRequestId)
            {
                <p>
                    <strong>Request ID:</strong> <code>@Model.RequestId</code>
                </p>
            }

            <h3 class="text-lg font-semibold text-gray-900 mb-3">Development Mode</h3>
            <p class="text-gray-600 mb-4">
                Swapping to the <strong>Development</strong> environment displays detailed information about the error that occurred.
            </p>
            <p class="text-gray-600">
                <strong>The Development environment shouldn't be enabled for deployed applications.</strong>
                It can result in displaying sensitive information from exceptions to end users.
                For local debugging, enable the <strong>Development</strong> environment by setting the <strong>ASPNETCORE_ENVIRONMENT</strong> environment variable to <strong>Development</strong>
                and restarting the app.
            </p>
        </div>
    </div>
</body>

</html>
