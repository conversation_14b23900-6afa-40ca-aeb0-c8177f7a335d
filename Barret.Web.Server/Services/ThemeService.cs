using Microsoft.JSInterop;

namespace Barret.Web.Server.Services;

/// <summary>
/// Service for managing application theme switching between light and dark modes
/// </summary>
public class ThemeService
{
    private readonly IJSRuntime _jsRuntime;
    private string _currentTheme = "light";

    public ThemeService(IJSRuntime jsRuntime)
    {
        _jsRuntime = jsRuntime;
    }

    /// <summary>
    /// Gets the current theme
    /// </summary>
    public string CurrentTheme => _currentTheme;

    /// <summary>
    /// Event fired when theme changes
    /// </summary>
    public event Action<string>? ThemeChanged;

    /// <summary>
    /// Switch to light theme
    /// </summary>
    public async Task SetLightThemeAsync()
    {
        await SetThemeAsync("light");
    }

    /// <summary>
    /// Switch to dark theme
    /// </summary>
    public async Task SetDarkThemeAsync()
    {
        await SetThemeAsync("dark");
    }

    /// <summary>
    /// Toggle between light and dark themes
    /// </summary>
    public async Task ToggleThemeAsync()
    {
        var newTheme = _currentTheme == "light" ? "dark" : "light";
        await SetThemeAsync(newTheme);
    }

    /// <summary>
    /// Set specific theme
    /// </summary>
    /// <param name="theme">Theme name: "light" or "dark"</param>
    public async Task SetThemeAsync(string theme)
    {
        if (theme != "light" && theme != "dark")
        {
            throw new ArgumentException("Theme must be 'light' or 'dark'", nameof(theme));
        }

        _currentTheme = theme;

        // Only call JavaScript if not prerendering
        try
        {
            // Update CSS link href via JavaScript
            await _jsRuntime.InvokeVoidAsync("updateTheme", theme);
        }
        catch (InvalidOperationException)
        {
            // JavaScript interop not available during prerendering - ignore
        }

        // Notify subscribers
        ThemeChanged?.Invoke(theme);
    }

    /// <summary>
    /// Initialize theme from browser storage or default to light
    /// </summary>
    public async Task InitializeThemeAsync()
    {
        try
        {
            // Try to get saved theme from localStorage
            var savedTheme = await _jsRuntime.InvokeAsync<string?>("localStorage.getItem", "barret-theme");

            if (!string.IsNullOrEmpty(savedTheme) && (savedTheme == "light" || savedTheme == "dark"))
            {
                await SetThemeAsync(savedTheme);
            }
            else
            {
                // Default to light theme
                await SetThemeAsync("light");
            }
        }
        catch (InvalidOperationException)
        {
            // JavaScript interop not available during prerendering
            // Just set the theme without JavaScript calls
            _currentTheme = "light";
            ThemeChanged?.Invoke(_currentTheme);
        }
        catch
        {
            // Fallback to light theme if localStorage is not available
            await SetThemeAsync("light");
        }
    }
}
