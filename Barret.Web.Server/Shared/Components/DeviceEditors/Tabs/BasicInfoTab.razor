@using Barret.Core.Areas.Devices.Enums
@using Barret.Shared.DTOs.Devices
@using Barret.Services.Core.Areas.Manufacturers
@using Microsoft.Extensions.Logging
@using Radzen.Blazor
@namespace Barret.Web.Server.Shared.Components.DeviceEditors.Tabs

<div class="row mb-3">
    <div class="col-md-12">
        <div class="form-group">
            <label for="deviceName" class="form-label">Name <span class="text-danger">*</span></label>
            <RadzenTextBox Value="@Device.Name"
                          ValueChanged="@OnNameChanged"
                          Placeholder="Enter device name"
                          ShowClearButton="true"
                          class="@($"form-input {(string.IsNullOrWhiteSpace(Device.Name) ? "form-input-error" : "")}")" />
            @if (string.IsNullOrWhiteSpace(Device.Name))
            {
                <div class="text-danger mt-1">
                    <small>Name is required</small>
                </div>
            }
        </div>
    </div>
</div>

@code {
    [Parameter]
    public DeviceDto Device { get; set; } = null!;

    [Parameter]
    public EventCallback OnPropertyChanged { get; set; }

    private async Task OnNameChanged(string newName)
    {
        if (Device.Name != newName)
        {
            Device.Name = newName;
            if (OnPropertyChanged.HasDelegate)
            {
                await OnPropertyChanged.InvokeAsync();
            }
        }
    }
}
