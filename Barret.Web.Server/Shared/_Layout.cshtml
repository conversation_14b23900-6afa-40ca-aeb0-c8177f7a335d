<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>@ViewData["Title"] - Barret</title>



    <!-- Bootstrap Icons -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.3/font/bootstrap-icons.min.css">

    <!-- Barret Light Theme (Complete Radzen Material + Design System) -->
    <link href="css/themes/radzen-barret.css" rel="stylesheet" id="theme-link" />

    <!-- Tailwind CSS with integrated design system -->
    <link href="css/Styles/dist.css" rel="stylesheet" />

    <!-- Additional styles -->
    @await RenderSectionAsync("Styles", required: false)
</head>

<body>
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <main role="main" class="pb-3">
            @RenderBody()
        </main>
    </div>

    <script src="_framework/blazor.server.js"></script>

    <script
        src="_content/Radzen.Blazor/Radzen.Blazor.js?v=@(typeof(Radzen.Colors).Assembly.GetName().Version)"></script>
    <!-- Scripts Section -->
    @await RenderSectionAsync("Scripts", required: false)
</body>

</html>
 />