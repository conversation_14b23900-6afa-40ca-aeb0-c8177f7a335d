```markdown
# Tailwind CSS Style Guide

## Table of Contents
1. [Color Palette](#color-palette)
2. [Typography](#typography)
3. [Spacing System](#spacing-system)
4. [Layout & Grid](#layout--grid)
5. [Components](#components)
6. [Interactive States](#interactive-states)
7. [Responsive Design](#responsive-design)
8. [Shadows & Effects](#shadows--effects)
9. [Border Radius](#border-radius)
10. [Best Practices](#best-practices)

---

## Color Palette

### Primary Colors
\`\`\`css
/* Primary Brand Colors */
bg-gray-900     /* Primary dark background */
bg-gray-800     /* Primary dark hover state */
text-white      /* Primary contrast text */

/* Example Usage */
<Button className="bg-gray-900 text-white hover:bg-gray-800">
\`\`\`

### Background Colors
\`\`\`css
/* Page Backgrounds */
bg-white        /* Main page background */
bg-gray-50      /* Sidebar, secondary sections */
bg-gray-100     /* Subtle highlights, disabled states */

/* Card Backgrounds */
bg-white        /* Primary cards */
bg-gray-50      /* Secondary cards, table headers */
\`\`\`

### Text Colors
\`\`\`css
/* Text Hierarchy */
text-gray-900   /* Primary headings, important text */
text-gray-700   /* Secondary text, labels */
text-gray-600   /* Body text */
text-gray-500   /* Muted text, captions, placeholders */
text-gray-400   /* Icons, very subtle text */

/* Example Usage */
<h1 className="text-2xl font-medium text-gray-900">Primary Heading</h1>
<p className="text-gray-600">Body text content</p>
<span className="text-xs text-gray-500">Caption or metadata</span>
\`\`\`

### Border Colors
\`\`\`css
/* Border Hierarchy */
border-gray-100  /* Subtle borders, table rows */
border-gray-200  /* Standard borders, inputs */
border-gray-300  /* Emphasized borders, focus states */

/* Example Usage */
<div className="border border-gray-100 rounded-xl">
<Input className="border-gray-200 focus:border-gray-300">
\`\`\`

### Status Colors
\`\`\`css
/* Success States */
bg-green-50     /* Success background */
bg-green-100    /* Success badge background */
text-green-500  /* Success icons */
text-green-800  /* Success text */

/* Error/Danger States */
bg-red-50       /* Error background */
bg-red-100      /* Error badge background */
text-red-500    /* Error icons */
text-red-600    /* Error buttons */
text-red-800    /* Error text */

/* Warning States */
bg-amber-100    /* Warning badge background */
text-amber-800  /* Warning text */

/* Example Usage */
<Badge className="bg-green-100 text-green-800">Active</Badge>
<Badge className="bg-red-100 text-red-800">Error</Badge>
<Button className="bg-red-500 text-white hover:bg-red-600">Delete</Button>
\`\`\`

---

## Typography

### Font Families
\`\`\`css
/* Default system font stack is used throughout */
font-sans       /* Applied globally via Tailwind defaults */
\`\`\`

### Font Sizes
\`\`\`css
/* Heading Hierarchy */
text-3xl        /* Page titles (h1) */
text-2xl        /* Section titles (h2) */
text-xl         /* Subsection titles (h3) */
text-lg         /* Component titles (h4) */

/* Body Text */
text-base       /* Default body text (16px) */
text-sm         /* Secondary text, labels (14px) */
text-xs         /* Captions, metadata (12px) */

/* Example Usage */
<h1 className="text-3xl font-medium text-gray-900">Vehicle Configuration System</h1>
<h2 className="text-2xl font-medium text-gray-900">Admin Dashboard</h2>
<h3 className="text-xl font-medium text-gray-900">Manufacturers</h3>
<h4 className="text-lg font-medium text-gray-900">Device Summary</h4>
<p className="text-gray-600">Regular body text content</p>
<label className="text-sm font-medium text-gray-700">Form Label</label>
<span className="text-xs text-gray-500">Last modified: Oct 15, 2023</span>
\`\`\`

### Font Weights
\`\`\`css
/* Weight Hierarchy */
font-medium     /* Headings, labels, important text */
font-semibold   /* Emphasized numbers, statistics */
font-normal     /* Default body text */

/* Example Usage */
<h2 className="text-lg font-medium text-gray-900">Section Title</h2>
<p className="text-3xl font-semibold text-gray-900">24</p>
<p className="text-gray-600">Regular paragraph text</p>
\`\`\`

---

## Spacing System

### Padding
\`\`\`css
/* Component Padding */
p-2             /* 8px - Small buttons, badges */
p-3             /* 12px - Medium buttons */
p-4             /* 16px - Cards, form sections */
p-5             /* 20px - Card content */
p-6             /* 24px - Large cards, modals */
p-8             /* 32px - Page sections */

/* Directional Padding */
px-3 py-2       /* Buttons, form inputs */
px-4 py-3       /* Larger buttons */
px-6 py-4       /* Modal content */

/* Example Usage */
<Button className="h-10 px-4 rounded-full">Standard Button</Button>
<Card className="p-6">Card with standard padding</Card>
<Input className="h-10 px-3 py-2">Form input</Input>
\`\`\`

### Margins
\`\`\`css
/* Vertical Spacing */
mb-2            /* 8px - Small gaps */
mb-4            /* 16px - Standard section spacing */
mb-6            /* 24px - Large section spacing */
mb-8            /* 32px - Page section spacing */
mb-16           /* 64px - Major section breaks */

/* Top Margins */
mt-1            /* 4px - Small adjustments */
mt-2            /* 8px - Minor spacing */
mt-4            /* 16px - Standard spacing */

/* Example Usage */
<header className="mb-8">Page header with bottom margin</header>
<div className="space-y-4">Container with consistent child spacing</div>
<p className="text-gray-500 mt-1">Helper text with small top margin</p>
\`\`\`

### Gaps
\`\`\`css
/* Flexbox/Grid Gaps */
gap-1           /* 4px - Tight spacing */
gap-2           /* 8px - Small spacing */
gap-3           /* 12px - Medium spacing */
gap-4           /* 16px - Standard spacing */
gap-6           /* 24px - Large spacing */

/* Example Usage */
<div className="flex items-center gap-2">Icon and text</div>
<div className="grid grid-cols-3 gap-6">Card grid</div>
<div className="flex justify-end gap-3">Button group</div>
\`\`\`

---

## Layout & Grid

### Container Patterns
\`\`\`css
/* Page Containers */
max-w-[1200px] mx-auto px-6 py-8    /* Main page container */
max-w-[1400px] mx-auto px-4 sm:px-6 /* Wide page container */
max-w-5xl mx-auto                   /* Content container */

/* Example Usage */
<div className="max-w-[1400px] mx-auto px-6 py-8">
  <main className="max-w-5xl mx-auto">
    <!-- Content -->
  </main>
</div>
\`\`\`

### Grid Systems
\`\`\`css
/* Responsive Grids */
grid-cols-1 md:grid-cols-2 lg:grid-cols-3    /* 1-2-3 responsive grid */
grid-cols-1 md:grid-cols-2 lg:grid-cols-4    /* 1-2-4 responsive grid */
grid-cols-1 sm:grid-cols-2 lg:grid-cols-3    /* Alternative breakpoints */

/* Table-like Grids */
grid-cols-12                                  /* 12-column layout */

/* Example Usage */
<div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
  <!-- Cards -->
</div>

<div className="grid grid-cols-12 gap-4 p-4">
  <div className="col-span-4">Name</div>
  <div className="col-span-2">Status</div>
  <div className="col-span-6">Actions</div>
</div>
\`\`\`

### Flexbox Patterns
\`\`\`css
/* Common Flex Patterns */
flex items-center justify-between    /* Header layouts */
flex items-center gap-3             /* Icon + text combinations */
flex justify-end gap-3              /* Button groups */
flex flex-col                       /* Vertical stacking */

/* Example Usage */
<header className="flex items-center justify-between mb-8">
  <h1>Title</h1>
  <div className="flex gap-3">
    <Button>Action 1</Button>
    <Button>Action 2</Button>
  </div>
</header>
\`\`\`

---

## Components

### Buttons
\`\`\`css
/* Primary Button */
.btn-primary {
  @apply h-10 px-4 rounded-full bg-gray-900 text-white hover:bg-gray-800 transition-colors;
}

/* Secondary Button */
.btn-secondary {
  @apply h-10 px-4 rounded-full border border-gray-200 text-gray-700 hover:bg-gray-50 hover:text-gray-900 transition-colors;
}

/* Danger Button */
.btn-danger {
  @apply h-10 px-4 rounded-full bg-red-500 text-white hover:bg-red-600 transition-colors;
}

/* Small Button */
.btn-small {
  @apply h-8 px-3 rounded-full text-sm;
}

/* Icon Button */
.btn-icon {
  @apply h-10 w-10 rounded-full flex items-center justify-center;
}

/* Example Usage */
<Button className="h-10 px-4 rounded-full bg-gray-900 text-white hover:bg-gray-800">
  Primary Action
</Button>
<Button className="h-8 px-3 rounded-full border border-gray-200 text-gray-700 hover:bg-gray-50">
  Secondary
</Button>
\`\`\`

### Cards
\`\`\`css
/* Standard Card */
.card {
  @apply bg-white rounded-xl border border-gray-100 shadow-sm hover:shadow-md transition-shadow;
}

/* Card Content */
.card-content {
  @apply p-6;
}

/* Subtle Card */
.card-subtle {
  @apply bg-gray-50 rounded-xl border-0;
}

/* Example Usage */
<Card className="bg-white rounded-xl border border-gray-100 shadow-sm hover:shadow-md transition-shadow">
  <CardContent className="p-6">
    <!-- Content -->
  </CardContent>
</Card>
\`\`\`

### Form Elements
\`\`\`css
/* Input Fields */
.input {
  @apply h-10 px-3 rounded-lg border border-gray-200 focus:border-gray-300 focus:ring focus:ring-gray-200 focus:ring-opacity-50;
}

/* Labels */
.label {
  @apply text-sm font-medium text-gray-700;
}

/* Error States */
.input-error {
  @apply border-red-300 focus:border-red-500 focus:ring focus:ring-red-200;
}

.label-error {
  @apply text-red-500;
}

/* Example Usage */
<Label className="text-sm font-medium text-gray-700">Field Label</Label>
<Input className="h-10 px-3 rounded-lg border border-gray-200 focus:border-gray-300 focus:ring focus:ring-gray-200 focus:ring-opacity-50" />
\`\`\`

### Badges
\`\`\`css
/* Status Badges */
.badge-success {
  @apply bg-green-100 text-green-800 hover:bg-green-100 px-2 py-1 rounded-full text-xs font-medium;
}

.badge-error {
  @apply bg-red-100 text-red-800 hover:bg-red-100 px-2 py-1 rounded-full text-xs font-medium;
}

.badge-warning {
  @apply bg-amber-100 text-amber-800 hover:bg-amber-100 px-2 py-1 rounded-full text-xs font-medium;
}

.badge-neutral {
  @apply bg-gray-100 text-gray-800 hover:bg-gray-100 px-2 py-1 rounded-full text-xs font-medium;
}

/* Example Usage */
<Badge className="bg-green-100 text-green-800 hover:bg-green-100">Active</Badge>
<Badge className="bg-red-100 text-red-800 hover:bg-red-100">Error</Badge>
\`\`\`

### Tables
\`\`\`css
/* Table Container */
.table-container {
  @apply bg-white rounded-xl overflow-hidden border border-gray-100;
}

/* Table Header */
.table-header {
  @apply bg-gray-50 border-b border-gray-100;
}

/* Table Header Cell */
.table-header-cell {
  @apply text-gray-500 font-medium py-3 px-4;
}

/* Table Row */
.table-row {
  @apply border-b border-gray-50 hover:bg-gray-50;
}

/* Table Cell */
.table-cell {
  @apply py-3 px-4 text-gray-600;
}

/* Example Usage */
<div className="bg-white rounded-xl overflow-hidden border border-gray-100">
  <Table>
    <TableHeader>
      <TableRow className="bg-gray-50 border-b border-gray-100">
        <TableHead className="text-gray-500 font-medium">Name</TableHead>
      </TableRow>
    </TableHeader>
    <TableBody>
      <TableRow className="border-b border-gray-50 hover:bg-gray-50">
        <TableCell className="text-gray-600">Content</TableCell>
      </TableRow>
    </TableBody>
  </Table>
</div>
\`\`\`

### Modals
\`\`\`css
/* Modal Content */
.modal-content {
  @apply sm:max-w-[600px] p-0 overflow-hidden bg-white rounded-xl border-0 shadow-lg;
}

/* Modal Header */
.modal-header {
  @apply p-6 pb-2 border-b border-gray-50;
}

/* Modal Body */
.modal-body {
  @apply p-6 overflow-y-auto max-h-[calc(100vh-200px)];
}

/* Modal Footer */
.modal-footer {
  @apply p-6 pt-2 border-t border-gray-50 flex justify-end gap-3;
}

/* Example Usage */
<DialogContent className="sm:max-w-[600px] p-0 overflow-hidden">
  <DialogHeader className="p-6 pb-2 border-b border-gray-50">
    <DialogTitle>Modal Title</DialogTitle>
  </DialogHeader>
  <div className="p-6">Modal content</div>
  <div className="p-6 pt-2 border-t border-gray-50 flex justify-end gap-3">
    <Button>Actions</Button>
  </div>
</DialogContent>
\`\`\`

---

## Interactive States

### Hover States
\`\`\`css
/* Button Hovers */
hover:bg-gray-800     /* Primary button hover */
hover:bg-gray-50      /* Secondary button hover */
hover:bg-red-600      /* Danger button hover */
hover:shadow-md       /* Card hover */
hover:bg-gray-100     /* Table row hover */

/* Text Hovers */
hover:text-gray-900   /* Link hover */
hover:text-red-600    /* Danger link hover */
\`\`\`

### Focus States
\`\`\`css
/* Input Focus */
focus:border-gray-300 focus:ring focus:ring-gray-200 focus:ring-opacity-50

/* Button Focus */
focus:outline-none focus:ring-2 focus:ring-gray-200 focus:ring-offset-2

/* Error Focus */
focus:border-red-500 focus:ring focus:ring-red-200
\`\`\`

### Active/Selected States
\`\`\`css
/* Active Tab */
data-[state=active]:border-gray-900 data-[state=active]:bg-transparent data-[state=active]:text-gray-900

/* Selected Item */
bg-white text-gray-900 shadow-sm    /* Selected sidebar item */
border-gray-900 ring-2 ring-gray-100 shadow-sm    /* Selected card */
\`\`\`

### Disabled States
\`\`\`css
/* Disabled Button */
disabled:cursor-not-allowed disabled:opacity-50

/* Disabled Input */
disabled:bg-gray-50 disabled:text-gray-500
\`\`\`

---

## Responsive Design

### Breakpoint Strategy
\`\`\`css
/* Mobile First Approach */
/* Default: Mobile (< 640px) */
sm:   /* Small devices (≥ 640px) */
md:   /* Medium devices (≥ 768px) */
lg:   /* Large devices (≥ 1024px) */
xl:   /* Extra large devices (≥ 1280px) */
\`\`\`

### Common Responsive Patterns
\`\`\`css
/* Grid Responsiveness */
grid-cols-1 md:grid-cols-2 lg:grid-cols-3
grid-cols-1 sm:grid-cols-2 lg:grid-cols-3

/* Flex Direction */
flex-col sm:flex-row

/* Text Sizes */
text-xl sm:text-2xl

/* Spacing */
px-4 sm:px-6
py-4 sm:py-6
gap-4 sm:gap-6

/* Visibility */
hidden md:flex        /* Hide on mobile, show on desktop */
md:hidden            /* Show on mobile, hide on desktop */

/* Sidebar Responsive */
fixed md:static      /* Fixed on mobile, static on desktop */
-translate-x-full md:translate-x-0    /* Hidden on mobile, visible on desktop */

/* Example Usage */
<div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 sm:gap-6">
<h1 className="text-xl sm:text-2xl font-medium">
<div className="flex flex-col sm:flex-row sm:items-center justify-between gap-4">
<Button className="h-9 sm:h-10 px-3 sm:px-4">
\`\`\`

---

## Shadows & Effects

### Shadow Hierarchy
\`\`\`css
/* Subtle Shadows */
shadow-sm           /* Cards, subtle elevation */

/* Standard Shadows */
shadow-md           /* Hover states, modals */

/* No Shadow */
shadow-none         /* Flat design elements */

/* Example Usage */
<Card className="shadow-sm hover:shadow-md transition-shadow">
<DialogContent className="shadow-lg">
\`\`\`

### Transitions
\`\`\`css
/* Standard Transitions */
transition-colors     /* Color changes */
transition-shadow     /* Shadow changes */
transition-all        /* All properties */

/* Duration */
duration-300         /* Standard duration */

/* Example Usage */
<Button className="bg-gray-900 hover:bg-gray-800 transition-colors">
<Card className="shadow-sm hover:shadow-md transition-shadow duration-300">
\`\`\`

---

## Border Radius

### Radius Scale
\`\`\`css
/* Border Radius Hierarchy */
rounded-none        /* 0px - Sharp corners */
rounded-sm          /* 2px - Subtle rounding */
rounded             /* 4px - Default rounding */
rounded-md          /* 6px - Medium rounding */
rounded-lg          /* 8px - Large rounding (inputs, cards) */
rounded-xl          /* 12px - Extra large (large cards) */
rounded-full        /* 50% - Circular (buttons, badges, avatars) */

/* Example Usage */
<Button className="rounded-full">Pill button</Button>
<Input className="rounded-lg">Form input</Input>
<Card className="rounded-xl">Large card</Card>
<Badge className="rounded-full">Status badge</Badge>
\`\`\`

---

## Best Practices

### Consistency Guidelines

1. **Color Usage**
   - Use gray-900 for primary actions and headings
   - Use gray-50 for subtle backgrounds
   - Reserve red colors for errors and destructive actions
   - Use green colors for success states only

2. **Typography**
   - Always pair font sizes with appropriate font weights
   - Use font-medium for headings and labels
   - Maintain consistent text color hierarchy

3. **Spacing**
   - Use consistent spacing scale (4px increments)
   - Prefer space-y-* for vertical spacing in containers
   - Use gap-* for flexbox and grid layouts

4. **Components**
   - Always include hover states for interactive elements
   - Use rounded-full for buttons and badges
   - Use rounded-lg for form inputs
   - Use rounded-xl for cards

5. **Responsive Design**
   - Design mobile-first
   - Use consistent breakpoint patterns
   - Test all responsive states

### Common Patterns

\`\`\`css
/* Page Header Pattern */
<header className="flex items-center justify-between mb-8">
  <h1 className="text-2xl font-medium text-gray-900">Page Title</h1>
  <div className="flex gap-3">
    <Button variant="outline">Secondary</Button>
    <Button>Primary</Button>
  </div>
</header>

/* Card Grid Pattern */
<div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
  <Card className="border-0 shadow-sm hover:shadow-md transition-shadow">
    <CardContent className="p-6">
      <!-- Content -->
    </CardContent>
  </Card>
</div>

/* Form Section Pattern */
<div className="space-y-4">
  <div className="space-y-2">
    <Label className="text-sm font-medium text-gray-700">Field Label</Label>
    <Input className="h-10 rounded-lg border-gray-200 focus:border-gray-300 focus:ring focus:ring-gray-200 focus:ring-opacity-50" />
  </div>
</div>

/* Status Indicator Pattern */
<div className="flex items-center gap-2">
  <div className="h-2 w-2 rounded-full bg-green-500"></div>
  <span className="text-sm text-gray-600">Active</span>
</div>

/* Navigation Tab Pattern */
<TabsTrigger className="rounded-none border-b-2 border-transparent data-[state=active]:border-gray-900 data-[state=active]:bg-transparent py-3 px-6 text-gray-500 data-[state=active]:text-gray-900 font-medium">
  Tab Label
</TabsTrigger>
\`\`\`

### Utility Combinations

\`\`\`css
/* Common utility combinations that work well together */

/* Centered content container */
max-w-5xl mx-auto px-6 py-8

/* Button with icon */
flex items-center gap-2 h-10 px-4 rounded-full

/* Card with hover effect */
bg-white rounded-xl border border-gray-100 shadow-sm hover:shadow-md transition-shadow

/* Form input with focus states */
h-10 px-3 rounded-lg border border-gray-200 focus:border-gray-300 focus:ring focus:ring-gray-200 focus:ring-opacity-50

/* Responsive grid */
grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6

/* Status badge */
bg-green-100 text-green-800 hover:bg-green-100 px-2 py-1 rounded-full text-xs font-medium

/* Sidebar navigation item */
w-full flex items-center gap-3 px-3 py-2.5 rounded-lg text-sm font-medium transition-colors

/* Table row */
border-b border-gray-50 hover:bg-gray-50

/* Modal content */
sm:max-w-[600px] p-0 overflow-hidden bg-white rounded-xl border-0 shadow-lg
\`\`\`

This style guide should be referenced for all new development to maintain visual consistency across the application. When in doubt, follow the established patterns and use the utility combinations provided above.
\`\`\`


```