# Barret Unified Color System

## Overview

I've implemented a unified color system that provides a single source of truth for all colors across your application. This system uses CSS custom properties (variables) to ensure consistency and makes theme switching seamless.

## Your Brand Colors

The system is built around your specified color palette:

```css
/* Primary Teal Colors */
--barret-teal-dark: #006666
--barret-teal-primary: #008080    /* Primary accent color */
--barret-teal-light: #009999
--barret-teal-muted: #8CB0A9

/* Supporting Colors */
--barret-green-light: #F3FFF3
--barret-gray-light: #DDE7E5
--barret-gray-medium: #D9D9D9
--barret-gray-dark: #B9B9B9

/* Base Colors */
--barret-white: #ffffff
--barret-black: #000000
```

## Architecture

### Single Source of Truth
All colors are defined once in the `:root` section of each theme file using `--barret-*` variables.

### Semantic Mapping
Radzen variables are mapped to your brand colors:
```css
--rz-primary: var(--barret-teal-primary);
--rz-primary-light: var(--barret-teal-light);
--rz-primary-dark: var(--barret-teal-dark);
```

### Theme Adaptation
- **Light Theme**: Uses your colors directly
- **Dark Theme**: Adapts colors for dark backgrounds while maintaining brand identity

## Component Classes

### Button System
```css
.barret-btn-primary    /* Primary teal buttons */
.barret-btn-secondary  /* White/gray buttons */
.barret-btn-accent     /* Light teal accent buttons */
```

### Grid System
```css
.barret-grid           /* Data grid styling */
.barret-grid-actions   /* Action button containers */
```

### Input System
```css
.barret-input          /* Form input styling */
```

### Card System
```css
.card-with-shadow      /* Card hover effects */
```

## Benefits

1. **Consistency**: All components use the same color values
2. **Maintainability**: Change colors in one place, updates everywhere
3. **Theme Support**: Automatic light/dark theme adaptation
4. **Performance**: No hardcoded colors, better for theme switching
5. **Scalability**: Easy to add new color variants

## Usage Examples

```html
<!-- Buttons -->
<RadzenButton class="barret-btn-primary" Text="Primary Action" />
<RadzenButton class="barret-btn-secondary" Text="Secondary Action" />

<!-- Data Grid -->
<RadzenDataGrid class="barret-grid" Data="@data" />

<!-- Form Input -->
<RadzenTextBox class="barret-input" Placeholder="Enter text..." />

<!-- Card -->
<RadzenCard class="card-with-shadow">Content</RadzenCard>
```

## Color Reference

### Light Theme
- Primary: #008080 (Teal)
- Background: #ffffff (White)
- Text: #000000 (Black)
- Borders: #D9D9D9 (Gray)

### Dark Theme
- Primary: #009999 (Light Teal)
- Background: #1a1a1a (Dark)
- Text: #ffffff (White)
- Borders: #3a3a3a (Dark Gray)

## Migration Benefits

This system eliminates:
- Hardcoded color values scattered throughout CSS
- Inconsistent color usage across components
- Difficulty in maintaining theme consistency
- Complex theme switching logic

All colors now flow from your brand palette through semantic mappings to component styling, ensuring perfect consistency across your entire application.
