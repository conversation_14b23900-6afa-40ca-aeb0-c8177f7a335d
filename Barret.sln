﻿
Microsoft Visual Studio Solution File, Format Version 12.00
# Visual Studio Version 17
VisualStudioVersion = 17.0.31903.59
MinimumVisualStudioVersion = 10.0.40219.1
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "Barret.Core", "Barret.Core\Barret.Core.csproj", "{34566625-4555-4C5A-B41D-7AA40F552D9F}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "Barret.Services", "Barret.Services\Barret.Services.csproj", "{60BF62A6-DC9E-4E15-B018-76F3F84C825C}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "Barret.Web.Server", "Barret.Web.Server\Barret.Web.Server.csproj", "{48E23B20-0853-49A0-A4E3-4BD23B8EB3A0}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "Barret.Shared", "Barret.Shared\Barret.Shared.csproj", "{10728A25-9DE1-46BD-9708-BD33897324AA}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "Tests", "Tests", "{D92C3AF2-BA14-4F11-BCEB-E330A3F33D39}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "Barret.Services.Tests.Unit", "Tests\Barret.Services.Tests.Unit\Barret.Services.Tests.Unit.csproj", "{47927C0A-2CA7-4480-BEC9-3CFD97B8AD23}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "Barret.Services.Core", "Barret.Services.Core\Barret.Services.Core.csproj", "{B548A8EC-6B72-4A26-802D-DD5C8B356374}"
EndProject
Global
	GlobalSection(SolutionConfigurationPlatforms) = preSolution
		Debug|Any CPU = Debug|Any CPU
		Release|Any CPU = Release|Any CPU
	EndGlobalSection
	GlobalSection(SolutionProperties) = preSolution
		HideSolutionNode = FALSE
	EndGlobalSection
	GlobalSection(ProjectConfigurationPlatforms) = postSolution
		{34566625-4555-4C5A-B41D-7AA40F552D9F}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{34566625-4555-4C5A-B41D-7AA40F552D9F}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{34566625-4555-4C5A-B41D-7AA40F552D9F}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{34566625-4555-4C5A-B41D-7AA40F552D9F}.Release|Any CPU.Build.0 = Release|Any CPU
		{60BF62A6-DC9E-4E15-B018-76F3F84C825C}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{60BF62A6-DC9E-4E15-B018-76F3F84C825C}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{60BF62A6-DC9E-4E15-B018-76F3F84C825C}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{60BF62A6-DC9E-4E15-B018-76F3F84C825C}.Release|Any CPU.Build.0 = Release|Any CPU
		{48E23B20-0853-49A0-A4E3-4BD23B8EB3A0}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{48E23B20-0853-49A0-A4E3-4BD23B8EB3A0}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{48E23B20-0853-49A0-A4E3-4BD23B8EB3A0}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{48E23B20-0853-49A0-A4E3-4BD23B8EB3A0}.Release|Any CPU.Build.0 = Release|Any CPU
		{10728A25-9DE1-46BD-9708-BD33897324AA}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{10728A25-9DE1-46BD-9708-BD33897324AA}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{10728A25-9DE1-46BD-9708-BD33897324AA}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{10728A25-9DE1-46BD-9708-BD33897324AA}.Release|Any CPU.Build.0 = Release|Any CPU

		{47927C0A-2CA7-4480-BEC9-3CFD97B8AD23}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{47927C0A-2CA7-4480-BEC9-3CFD97B8AD23}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{47927C0A-2CA7-4480-BEC9-3CFD97B8AD23}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{47927C0A-2CA7-4480-BEC9-3CFD97B8AD23}.Release|Any CPU.Build.0 = Release|Any CPU
		{B548A8EC-6B72-4A26-802D-DD5C8B356374}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{B548A8EC-6B72-4A26-802D-DD5C8B356374}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{B548A8EC-6B72-4A26-802D-DD5C8B356374}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{B548A8EC-6B72-4A26-802D-DD5C8B356374}.Release|Any CPU.Build.0 = Release|Any CPU
	EndGlobalSection
	GlobalSection(NestedProjects) = preSolution
		{47927C0A-2CA7-4480-BEC9-3CFD97B8AD23} = {D92C3AF2-BA14-4F11-BCEB-E330A3F33D39}
	EndGlobalSection
EndGlobal
