# Bootstrap to Radzen Migration Guide

This guide provides comprehensive instructions for migrating Bootstrap components to Radzen Blazor components while maintaining the unified Barret design system.

## Overview

The Barret application is transitioning from Bootstrap to Radzen Blazor components to achieve:
- **Unified Component Library**: Single source of truth for UI components
- **Enhanced Styling**: Consistent design system with improved theming
- **Better Integration**: Native Blazor components with better performance
- **Maintainability**: Reduced CSS complexity and improved code organization

## Enhanced Radzen Styling

The application now includes enhanced Radzen component styling in `barret-theme.css` that automatically applies Barret design system styling to all Radzen components. This ensures visual consistency with existing Bootstrap components.

## Migration Patterns

### 1. Bootstrap Cards → RadzenCard

**Before (Bootstrap):**
```razor
<div class="card">
    <div class="card-header">
        <h5 class="card-title">Title</h5>
    </div>
    <div class="card-body">
        <p class="card-text">Content</p>
    </div>
    <div class="card-footer">
        Footer content
    </div>
</div>
```

**After (Radzen):**
```razor
<RadzenCard class="mb-4">
    <RadzenStack Gap="1rem">
        <RadzenText TextStyle="TextStyle.H5" TagName="TagName.H5">Title</RadzenText>
        <RadzenText>Content</RadzenText>
        <div class="border-t border-gray-200 pt-3 mt-3">
            Footer content
        </div>
    </RadzenStack>
</RadzenCard>
```

### 2. Bootstrap Buttons → RadzenButton

**Before (Bootstrap):**
```razor
<button class="btn btn-primary">Primary</button>
<button class="btn btn-secondary">Secondary</button>
<button class="btn btn-outline-primary">Outline</button>
```

**After (Radzen):**
```razor
<RadzenButton Text="Primary" ButtonStyle="ButtonStyle.Primary" class="barret-btn" />
<RadzenButton Text="Secondary" ButtonStyle="ButtonStyle.Secondary" class="barret-btn" />
<RadzenButton Text="Outline" ButtonStyle="ButtonStyle.Light" class="barret-btn" />
```

### 3. Bootstrap Forms → RadzenFormField

**Before (Bootstrap):**
```razor
<div class="form-group mb-3">
    <label for="email" class="form-label">Email</label>
    <input type="email" class="form-control" id="email" @bind="Email" />
    <div class="form-text">Enter your email address</div>
</div>
```

**After (Radzen):**
```razor
<RadzenFormField Text="Email" Variant="Variant.Outlined">
    <RadzenTextBox @bind-Value="@Email" 
                   Placeholder="Enter your email address"
                   class="barret-input w-full" />
</RadzenFormField>
```

### 4. Bootstrap Tables/Grids → RadzenDataGrid

**Before (Bootstrap Table):**
```razor
<table class="table table-striped">
    <thead>
        <tr>
            <th>Name</th>
            <th>Status</th>
            <th>Actions</th>
        </tr>
    </thead>
    <tbody>
        @foreach (var item in Items)
        {
            <tr>
                <td>@item.Name</td>
                <td>@item.Status</td>
                <td>
                    <button class="btn btn-sm btn-primary">Edit</button>
                    <button class="btn btn-sm btn-danger">Delete</button>
                </td>
            </tr>
        }
    </tbody>
</table>
```

**After (RadzenDataGrid):**
```razor
<RadzenDataGrid Data="@Items"
                TItem="ItemDto"
                AllowFiltering="true"
                AllowPaging="true"
                AllowSorting="true"
                PageSize="20"
                class="barret-grid">
    <Columns>
        <RadzenDataGridColumn TItem="ItemDto" Property="Name" Title="Name" />
        <RadzenDataGridColumn TItem="ItemDto" Property="Status" Title="Status" />
        <RadzenDataGridColumn TItem="ItemDto" Title="Actions" Sortable="false" Filterable="false">
            <Template Context="item">
                <div class="flex gap-2">
                    <RadzenButton Text="Edit"
                                  Icon="edit"
                                  ButtonStyle="ButtonStyle.Primary"
                                  Size="ButtonSize.Small"
                                  class="barret-btn" />
                    <RadzenButton Text="Delete"
                                  Icon="delete"
                                  ButtonStyle="ButtonStyle.Danger"
                                  Size="ButtonSize.Small"
                                  class="barret-btn" />
                </div>
            </Template>
        </RadzenDataGridColumn>
    </Columns>
</RadzenDataGrid>
```

### 4.1 Advanced RadzenDataGrid Features

**With Expandable Rows:**
```razor
<RadzenDataGrid Data="@Vehicles"
                TItem="VehicleDto"
                AllowFiltering="true"
                AllowPaging="true"
                PageSize="10"
                ExpandMode="DataGridExpandMode.Single"
                class="barret-grid">
    <Columns>
        <RadzenDataGridColumn TItem="VehicleDto" Property="Name" Title="Vehicle Name" />
        <RadzenDataGridColumn TItem="VehicleDto" Property="Type" Title="Type" />
        <RadzenDataGridColumn TItem="VehicleDto" Title="Actions" Sortable="false" Filterable="false">
            <Template Context="vehicle">
                <RadzenButton Icon="edit" ButtonStyle="ButtonStyle.Light" Size="ButtonSize.Small" />
                <RadzenButton Icon="delete" ButtonStyle="ButtonStyle.Danger" Size="ButtonSize.Small" />
            </Template>
        </RadzenDataGridColumn>
    </Columns>
    <Template Context="vehicle">
        <RadzenCard class="m-3">
            <RadzenText TextStyle="TextStyle.H6">Device Details</RadzenText>
            <RadzenDataGrid Data="@vehicle.Devices" TItem="DeviceDto" AllowPaging="false">
                <Columns>
                    <RadzenDataGridColumn TItem="DeviceDto" Property="Name" Title="Device Name" />
                    <RadzenDataGridColumn TItem="DeviceDto" Property="DeviceRole" Title="Role" />
                </Columns>
            </RadzenDataGrid>
        </RadzenCard>
    </Template>
</RadzenDataGrid>
```

**With Custom Filtering:**
```razor
<RadzenDataGrid Data="@FilteredItems"
                TItem="ItemDto"
                AllowFiltering="true"
                FilterMode="FilterMode.Advanced"
                LogicalFilterOperator="LogicalFilterOperator.And"
                class="barret-grid">
    <Columns>
        <RadzenDataGridColumn TItem="ItemDto" Property="Name" Title="Name" Width="200px">
            <FilterTemplate>
                <RadzenTextBox @bind-Value="@nameFilter"
                               Placeholder="Filter by name..."
                               @oninput="@(args => FilterData())"
                               class="barret-input w-full" />
            </FilterTemplate>
        </RadzenDataGridColumn>
        <RadzenDataGridColumn TItem="ItemDto" Property="Status" Title="Status" Width="150px">
            <FilterTemplate>
                <RadzenDropDown @bind-Value="@statusFilter"
                                Data="@StatusOptions"
                                AllowClear="true"
                                Placeholder="All Statuses"
                                ValueChanged="@(args => FilterData())"
                                class="barret-input w-full" />
            </FilterTemplate>
        </RadzenDataGridColumn>
    </Columns>
</RadzenDataGrid>
```

### 4.2 Bootstrap Layout Grid → Tailwind CSS Grid

**Before (Bootstrap Layout):**
```razor
<div class="row">
    <div class="col-md-6">Column 1</div>
    <div class="col-md-6">Column 2</div>
</div>
```

**After (Tailwind Layout):**
```razor
<div class="grid grid-cols-1 md:grid-cols-2 gap-4">
    <div>Column 1</div>
    <div>Column 2</div>
</div>
```

### 4.2 Bootstrap Layout Grid → RadzenRow/RadzenColumn

**Alternative (RadzenRow/RadzenColumn):**
```razor
<RadzenRow Gap="1rem">
    <RadzenColumn Size="12" SizeMD="6">Column 1</RadzenColumn>
    <RadzenColumn Size="12" SizeMD="6">Column 2</RadzenColumn>
</RadzenRow>
```

### 5. Bootstrap Dropdowns → RadzenDropDown

**Before (Bootstrap):**
```razor
<select class="form-select" @bind="SelectedValue">
    @foreach (var option in Options)
    {
        <option value="@option.Value">@option.Text</option>
    }
</select>
```

**After (Radzen):**
```razor
<RadzenFormField Text="Select Option" Variant="Variant.Outlined">
    <RadzenDropDown @bind-Value="@SelectedValue"
                    Data="@Options"
                    TextProperty="Text"
                    ValueProperty="Value"
                    Placeholder="Select an option"
                    class="barret-input w-full" />
</RadzenFormField>
```

## Complete Migration Example

Here's a complete example showing the migration of the ConnectionTab component:

**Before (Bootstrap):**
```razor
<div class="row mb-3">
    <div class="col-md-4">
        <div class="form-group">
            <label for="ipAddress" class="form-label">IP Address</label>
            <input type="text" class="form-control" @bind="Device.Connection.IPAddress" />
        </div>
    </div>
    <div class="col-md-4">
        <div class="form-group">
            <label for="port" class="form-label">Port</label>
            <input type="number" class="form-control" @bind="Device.Connection.Port" />
        </div>
    </div>
    <div class="col-md-4">
        <div class="form-group">
            <label for="protocol" class="form-label">Protocol</label>
            <select class="form-select" @bind="Device.Connection.Protocol">
                @foreach (var protocol in Enum.GetValues(typeof(Protocol)))
                {
                    <option value="@protocol">@protocol</option>
                }
            </select>
        </div>
    </div>
</div>
```

**After (Radzen):**
```razor
<div class="grid grid-cols-1 md:grid-cols-3 gap-4 mb-4">
    <RadzenFormField Text="IP Address" Variant="Variant.Outlined">
        <RadzenTextBox @bind-Value="@Device.Connection.IPAddress"
                       Placeholder="Enter IP address"
                       class="barret-input w-full" />
    </RadzenFormField>

    <RadzenFormField Text="Port" Variant="Variant.Outlined">
        <RadzenNumeric @bind-Value="@Device.Connection.Port"
                       Min="1" Max="65535"
                       Placeholder="Enter port number"
                       class="barret-input w-full" />
    </RadzenFormField>

    <RadzenFormField Text="Protocol" Variant="Variant.Outlined">
        <RadzenDropDown @bind-Value="@Device.Connection.Protocol"
                        Data="@ProtocolOptions"
                        TextProperty="Text"
                        ValueProperty="Value"
                        Placeholder="Select protocol"
                        class="barret-input w-full" />
    </RadzenFormField>
</div>
```

## Styling Classes

### Barret CSS Classes for Radzen Components

- **`.barret-btn`**: Apply to all RadzenButton components
- **`.barret-input`**: Apply to all form input components
- **`.barret-grid`**: Apply to RadzenDataGrid components
- **`.barret-dialog`**: Apply to dialog containers

### Tailwind Utility Classes

Use Tailwind classes for layout and spacing:
- **Grid**: `grid grid-cols-1 md:grid-cols-2 gap-4`
- **Flexbox**: `flex items-center justify-between gap-2`
- **Spacing**: `mb-4`, `mt-2`, `p-4`, `px-6 py-3`
- **Colors**: `text-gray-600`, `bg-white`, `border-gray-200`

## Migration Checklist

### Component Migration
- [ ] Replace Bootstrap cards with RadzenCard
- [ ] Replace Bootstrap buttons with RadzenButton
- [ ] Replace Bootstrap forms with RadzenFormField
- [ ] Replace Bootstrap dropdowns with RadzenDropDown
- [ ] Replace Bootstrap tables with RadzenDataGrid
- [ ] Replace Bootstrap layout grids with Tailwind CSS grid or RadzenRow/RadzenColumn
- [ ] Update all CSS class references

### Styling Updates
- [ ] Apply `barret-*` classes to Radzen components
- [ ] Use Tailwind utilities for layout
- [ ] Remove Bootstrap-specific CSS classes
- [ ] Test responsive behavior
- [ ] Verify design consistency

### Code Updates
- [ ] Add `@using Radzen.Blazor` directive
- [ ] Update data binding patterns
- [ ] Convert enum options to dropdown data
- [ ] Update event handlers
- [ ] Test component functionality

## Best Practices

1. **Always use RadzenFormField**: Wrap form inputs in RadzenFormField for consistent styling
2. **Apply barret-* classes**: Use the predefined Barret classes for consistent styling
3. **Use Tailwind for layout**: Prefer Tailwind grid/flexbox over Bootstrap grid
4. **Maintain accessibility**: Ensure proper labels and ARIA attributes
5. **Test responsiveness**: Verify components work on all screen sizes

## Common Issues and Solutions

### Issue: RadzenDropDown not displaying options
**Solution**: Ensure you're using `TextProperty` and `ValueProperty` correctly, and that your data source implements the required properties.

### Issue: Styling not applied
**Solution**: Verify that `barret-theme.css` is loaded after the base Radzen theme in your layout.

### Issue: Form validation not working
**Solution**: Use RadzenFormField with proper validation attributes and ensure error messages are displayed correctly.

## Resources

- [Radzen Blazor Components Documentation](https://blazor.radzen.com/)
- [Tailwind CSS Documentation](https://tailwindcss.com/docs)
- [Barret Design System Variables](../Barret.Web.Server/wwwroot/css/Styles/_variables.css)
- [Enhanced Radzen Theme](../Barret.Web.Server/wwwroot/css/barret-theme.css)
