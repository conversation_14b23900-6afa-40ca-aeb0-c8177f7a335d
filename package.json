{"name": "barret-vehicle-configurator", "version": "1.0.0", "description": "Barret Vehicle Configurator", "scripts": {"build:css": "npm run build:tailwind", "build:css:dev": "npm run build:tailwind:dev", "watch:css": "npm run watch:tailwind", "watch:css:poll": "npm run watch:tailwind:poll", "purge:css": "npm run build:tailwind:purge", "analyze:css": "tailwindcss -i ./Barret.Web.Server/wwwroot/css/Styles/main.css -o ./Barret.Web.Server/wwwroot/css/Styles/dist.css --content './Barret.Web.Server/**/*.{razor,html,cshtml}' --verbose", "build:tailwind": "tailwindcss -i ./Barret.Web.Server/wwwroot/css/Styles/main.css -o ./Barret.Web.Server/wwwroot/css/Styles/dist.css --minify", "build:tailwind:dev": "tailwindcss -i ./Barret.Web.Server/wwwroot/css/Styles/main.css -o ./Barret.Web.Server/wwwroot/css/Styles/dist.css", "build:tailwind:purge": "tailwindcss -i ./Barret.Web.Server/wwwroot/css/Styles/main.css -o ./Barret.Web.Server/wwwroot/css/Styles/dist.css --minify --purge", "watch:tailwind": "tailwindcss -i ./Barret.Web.Server/wwwroot/css/Styles/main.css -o ./Barret.Web.Server/wwwroot/css/Styles/dist.css --watch", "watch:tailwind:poll": "tailwindcss -i ./Barret.Web.Server/wwwroot/css/Styles/main.css -o ./Barret.Web.Server/wwwroot/css/Styles/dist.css --watch --poll"}, "dependencies": {"tailwindcss": "^3.4.17"}}