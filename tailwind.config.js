/** @type {import('tailwindcss').Config} */
export const content = [
  './Barret.Web.Server/**/*.{razor,html,cshtml}',
  './Barret.Web.Server/**/*.cs', // For dynamic class generation
];
export const theme = {
  extend: {
    // Align with CSS custom properties for consistency
    colors: {
      primary: 'var(--color-primary)',
      secondary: 'var(--color-secondary)',
      gray: {
        50: 'var(--color-gray-50)',
        100: 'var(--color-gray-100)',
        200: 'var(--color-gray-200)',
        300: 'var(--color-gray-300)',
        400: 'var(--color-gray-400)',
        500: 'var(--color-gray-500)',
        600: 'var(--color-gray-600)',
        700: 'var(--color-gray-700)',
        800: 'var(--color-gray-800)',
        900: 'var(--color-gray-900)',
      },
      success: 'var(--color-success)',
      warning: 'var(--color-warning)',
      danger: 'var(--color-danger)',
    },
    spacing: {
      // Use Tailwind's default spacing that aligns with our tokens
    },
    borderRadius: {
      // Use Tailwind's default border radius
    },
  },
};
export const plugins = [
  // Remove any @apply-based component definitions
];
export const safelist = [
  // Add classes that might be used dynamically
  'bg-white',
  'bg-gray-50',
  'bg-gray-100',
  'bg-gray-200',
  'bg-gray-800',
  'bg-gray-900',
  'text-white',
  'text-gray-500',
  'text-gray-700',
  'text-gray-900',
  'hover:bg-gray-800',
  'hover:shadow-md',
  'border-0',
  'border-gray-100',
  'border-gray-200',
  'rounded-xl',
  'rounded-full',
  'shadow-sm',
  'transition-shadow',
  'transition-colors',
  'duration-300',
  'h-full',
  'h-8',
  'h-12',
  'h-16',
  'w-8',
  'w-12',
  'w-16',
  'animate-spin',
  'border-t-gray-800',
  'border-4',
  'text-sm',
  'text-base',
  'text-lg',
  'text-xl',
  'text-2xl',
  'text-3xl',
  'font-normal',
  'font-medium',
  'max-w-[1200px]',
  'max-w-4xl',
  'grid-cols-1',
  'md:grid-cols-2',
  'lg:grid-cols-4',
  'gap-6',
  'mb-2',
  'mb-6',
  'mb-8',
  'md:mb-16',
  'mt-2',
  'mt-4',
  'mt-auto',
  'px-4',
  'px-6',
  'py-2',
  'py-8',
  'p-8',
  'flex-1',
  'flex-col',
  'items-center',
  'justify-center',
  'text-center',
  'overflow-hidden',
  'cursor-pointer',
  // New semantic component classes
  'btn',
  'btn-sm',
  'btn-md',
  'btn-lg',
  'btn-primary',
  'btn-secondary',
  'btn-success',
  'btn-danger',
  'card',
  'card-sm',
  'card-md',
  'card-lg',
  'card-interactive',
  'card-elevated',
  'card-outlined',
  'form-input',
  'form-label',
  'form-group',
  'form-error',
  'badge',
  'badge-success',
  'badge-warning',
  'badge-danger',
  'badge-neutral',
];
